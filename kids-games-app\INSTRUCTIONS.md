# 📋 تعليمات الاستخدام السريعة

## 🚀 البدء السريع

1. **تشغيل التطبيق**
   ```bash
   npm run dev
   ```

2. **فتح المتصفح**
   - انتقل إلى `http://localhost:5173`

3. **اختيار اللعبة**
   - انقر على أي لعبة من الشاشة الرئيسية

## 🎮 دليل الألعاب

### 🧠 لعبة الذاكرة
- **الهدف**: العثور على أزواج البطاقات المتطابقة
- **كيفية اللعب**: انقر على بطاقتين لكشفهما
- **النقاط**: 10 نقاط لكل زوج صحيح + مكافأة للحركات القليلة

### 🎨 تعلم الألوان
- **الهدف**: تعلم أسماء الألوان
- **كيفية اللعب**: اختر الاسم الصحيح للون المعروض
- **النقاط**: 20 نقطة لكل إجابة صحيحة

### 🔢 الأرقام المرحة
- **الأنماط المتاحة**:
  - **العد**: عد الأشياء المعروضة
  - **التعرف**: تحديد الرقم المعروض
  - **الحساب**: حل عمليات الجمع والطرح البسيطة
- **النقاط**: 20 نقطة لكل إجابة صحيحة

### 📝 عالم الحروف
- **الأنماط المتاحة**:
  - **العربية**: الحروف العربية
  - **English**: الحروف الإنجليزية
  - **مختلط**: خليط من الحروف العربية والإنجليزية
- **النقاط**: 15 نقطة لكل إجابة صحيحة

### 🎨 لوحة الرسم
- **الأدوات المتاحة**:
  - **الفرشاة**: للرسم بألوان مختلفة
  - **الممحاة**: لمسح أجزاء من الرسم
  - **تغيير حجم الفرشاة**: من 1 إلى 50 بكسل
- **الوظائف**:
  - **تراجع**: للعودة خطوة واحدة
  - **مسح الكل**: لمسح اللوحة بالكامل
  - **تحميل**: لحفظ الرسم كصورة

### 🧩 الألغاز الذكية
- **المستويات**:
  - **سهل**: شبكة 3×3 (8 قطع)
  - **متوسط**: شبكة 4×4 (15 قطعة)
  - **صعب**: شبكة 5×5 (24 قطعة)
- **كيفية اللعب**: انقر على القطع المجاورة للمربع الفارغ لتحريكها

## 🎯 نصائح للآباء والمعلمين

### للأطفال الصغار (3-5 سنوات)
- ابدأ بلعبة الألوان ولعبة الذاكرة
- استخدم لوحة الرسم لتطوير المهارات الحركية
- اللعب مع الطفل لتوجيهه

### للأطفال المتوسطين (6-8 سنوات)
- جرب جميع الألعاب
- ركز على الأرقام والحروف
- شجع الطفل على تحسين نقاطه

### للأطفال الأكبر (9+ سنوات)
- استخدم المستويات الصعبة في الألغاز
- جرب النمط المختلط في الحروف
- استخدم عمليات الحساب في الأرقام

## 🔧 استكشاف الأخطاء

### المشاكل الشائعة

**التطبيق لا يعمل:**
- تأكد من تثبيت Node.js
- تأكد من تشغيل `npm install` أولاً
- تحقق من أن المنفذ 5173 غير مستخدم

**الألعاب لا تظهر:**
- تحديث الصفحة (F5)
- تنظيف ذاكرة التخزين المؤقت للمتصفح
- تحقق من وحدة تحكم المطور للأخطاء

**مشاكل في الأداء:**
- أغلق التطبيقات الأخرى
- استخدم متصفح حديث
- تأكد من وجود ذاكرة كافية

## 📱 نصائح للاستخدام على الأجهزة المحمولة

- **لوحة الرسم**: استخدم الإصبع للرسم
- **الألعاب**: انقر بلطف على الأزرار
- **التنقل**: استخدم زر "العودة" في أعلى كل لعبة

## 🎨 التخصيص

### تغيير الألوان
- عدل ملفات `.jsx` في مجلد `src/components`
- ابحث عن `styled-components` وغير قيم الألوان

### إضافة محتوى جديد
- **ألوان جديدة**: عدل مصفوفة `colors` في `ColorsGame.jsx`
- **حروف جديدة**: عدل مصفوفات `arabicLetters` و `englishLetters`
- **أرقام جديدة**: عدل منطق توليد الأسئلة في `NumbersGame.jsx`

## 🆘 الحصول على المساعدة

إذا واجهت أي مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من ملف README.md
3. ابحث في وحدة تحكم المطور عن رسائل الخطأ
4. تأكد من أن جميع التبعيات مثبتة بشكل صحيح

---

**استمتع باللعب والتعلم! 🎉**
