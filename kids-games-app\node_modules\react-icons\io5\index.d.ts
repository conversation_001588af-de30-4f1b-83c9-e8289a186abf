// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const IoAccessibilityOutline: IconType;
export declare const IoAccessibilitySharp: IconType;
export declare const IoAccessibility: IconType;
export declare const IoAddCircleOutline: IconType;
export declare const IoAddCircleSharp: IconType;
export declare const IoAddCircle: IconType;
export declare const IoAddOutline: IconType;
export declare const IoAddSharp: IconType;
export declare const IoAdd: IconType;
export declare const IoAirplaneOutline: IconType;
export declare const IoAirplaneSharp: IconType;
export declare const IoAirplane: IconType;
export declare const IoAlarmOutline: IconType;
export declare const IoAlarmSharp: IconType;
export declare const IoAlarm: IconType;
export declare const IoAlbumsOutline: IconType;
export declare const IoAlbumsSharp: IconType;
export declare const IoAlbums: IconType;
export declare const IoAlertCircleOutline: IconType;
export declare const IoAlertCircleSharp: IconType;
export declare const IoAlertCircle: IconType;
export declare const IoAlertOutline: IconType;
export declare const IoAlertSharp: IconType;
export declare const IoAlert: IconType;
export declare const IoAmericanFootballOutline: IconType;
export declare const IoAmericanFootballSharp: IconType;
export declare const IoAmericanFootball: IconType;
export declare const IoAnalyticsOutline: IconType;
export declare const IoAnalyticsSharp: IconType;
export declare const IoAnalytics: IconType;
export declare const IoApertureOutline: IconType;
export declare const IoApertureSharp: IconType;
export declare const IoAperture: IconType;
export declare const IoAppsOutline: IconType;
export declare const IoAppsSharp: IconType;
export declare const IoApps: IconType;
export declare const IoArchiveOutline: IconType;
export declare const IoArchiveSharp: IconType;
export declare const IoArchive: IconType;
export declare const IoArrowBackCircleOutline: IconType;
export declare const IoArrowBackCircleSharp: IconType;
export declare const IoArrowBackCircle: IconType;
export declare const IoArrowBackOutline: IconType;
export declare const IoArrowBackSharp: IconType;
export declare const IoArrowBack: IconType;
export declare const IoArrowDownCircleOutline: IconType;
export declare const IoArrowDownCircleSharp: IconType;
export declare const IoArrowDownCircle: IconType;
export declare const IoArrowDownOutline: IconType;
export declare const IoArrowDownSharp: IconType;
export declare const IoArrowDown: IconType;
export declare const IoArrowForwardCircleOutline: IconType;
export declare const IoArrowForwardCircleSharp: IconType;
export declare const IoArrowForwardCircle: IconType;
export declare const IoArrowForwardOutline: IconType;
export declare const IoArrowForwardSharp: IconType;
export declare const IoArrowForward: IconType;
export declare const IoArrowRedoCircleOutline: IconType;
export declare const IoArrowRedoCircleSharp: IconType;
export declare const IoArrowRedoCircle: IconType;
export declare const IoArrowRedoOutline: IconType;
export declare const IoArrowRedoSharp: IconType;
export declare const IoArrowRedo: IconType;
export declare const IoArrowUndoCircleOutline: IconType;
export declare const IoArrowUndoCircleSharp: IconType;
export declare const IoArrowUndoCircle: IconType;
export declare const IoArrowUndoOutline: IconType;
export declare const IoArrowUndoSharp: IconType;
export declare const IoArrowUndo: IconType;
export declare const IoArrowUpCircleOutline: IconType;
export declare const IoArrowUpCircleSharp: IconType;
export declare const IoArrowUpCircle: IconType;
export declare const IoArrowUpOutline: IconType;
export declare const IoArrowUpSharp: IconType;
export declare const IoArrowUp: IconType;
export declare const IoAtCircleOutline: IconType;
export declare const IoAtCircleSharp: IconType;
export declare const IoAtCircle: IconType;
export declare const IoAtOutline: IconType;
export declare const IoAtSharp: IconType;
export declare const IoAt: IconType;
export declare const IoAttachOutline: IconType;
export declare const IoAttachSharp: IconType;
export declare const IoAttach: IconType;
export declare const IoBackspaceOutline: IconType;
export declare const IoBackspaceSharp: IconType;
export declare const IoBackspace: IconType;
export declare const IoBagAddOutline: IconType;
export declare const IoBagAddSharp: IconType;
export declare const IoBagAdd: IconType;
export declare const IoBagCheckOutline: IconType;
export declare const IoBagCheckSharp: IconType;
export declare const IoBagCheck: IconType;
export declare const IoBagHandleOutline: IconType;
export declare const IoBagHandleSharp: IconType;
export declare const IoBagHandle: IconType;
export declare const IoBagOutline: IconType;
export declare const IoBagRemoveOutline: IconType;
export declare const IoBagRemoveSharp: IconType;
export declare const IoBagRemove: IconType;
export declare const IoBagSharp: IconType;
export declare const IoBag: IconType;
export declare const IoBalloonOutline: IconType;
export declare const IoBalloonSharp: IconType;
export declare const IoBalloon: IconType;
export declare const IoBanOutline: IconType;
export declare const IoBanSharp: IconType;
export declare const IoBan: IconType;
export declare const IoBandageOutline: IconType;
export declare const IoBandageSharp: IconType;
export declare const IoBandage: IconType;
export declare const IoBarChartOutline: IconType;
export declare const IoBarChartSharp: IconType;
export declare const IoBarChart: IconType;
export declare const IoBarbellOutline: IconType;
export declare const IoBarbellSharp: IconType;
export declare const IoBarbell: IconType;
export declare const IoBarcodeOutline: IconType;
export declare const IoBarcodeSharp: IconType;
export declare const IoBarcode: IconType;
export declare const IoBaseballOutline: IconType;
export declare const IoBaseballSharp: IconType;
export declare const IoBaseball: IconType;
export declare const IoBasketOutline: IconType;
export declare const IoBasketSharp: IconType;
export declare const IoBasket: IconType;
export declare const IoBasketballOutline: IconType;
export declare const IoBasketballSharp: IconType;
export declare const IoBasketball: IconType;
export declare const IoBatteryChargingOutline: IconType;
export declare const IoBatteryChargingSharp: IconType;
export declare const IoBatteryCharging: IconType;
export declare const IoBatteryDeadOutline: IconType;
export declare const IoBatteryDeadSharp: IconType;
export declare const IoBatteryDead: IconType;
export declare const IoBatteryFullOutline: IconType;
export declare const IoBatteryFullSharp: IconType;
export declare const IoBatteryFull: IconType;
export declare const IoBatteryHalfOutline: IconType;
export declare const IoBatteryHalfSharp: IconType;
export declare const IoBatteryHalf: IconType;
export declare const IoBeakerOutline: IconType;
export declare const IoBeakerSharp: IconType;
export declare const IoBeaker: IconType;
export declare const IoBedOutline: IconType;
export declare const IoBedSharp: IconType;
export declare const IoBed: IconType;
export declare const IoBeerOutline: IconType;
export declare const IoBeerSharp: IconType;
export declare const IoBeer: IconType;
export declare const IoBicycleOutline: IconType;
export declare const IoBicycleSharp: IconType;
export declare const IoBicycle: IconType;
export declare const IoBluetoothOutline: IconType;
export declare const IoBluetoothSharp: IconType;
export declare const IoBluetooth: IconType;
export declare const IoBoatOutline: IconType;
export declare const IoBoatSharp: IconType;
export declare const IoBoat: IconType;
export declare const IoBodyOutline: IconType;
export declare const IoBodySharp: IconType;
export declare const IoBody: IconType;
export declare const IoBonfireOutline: IconType;
export declare const IoBonfireSharp: IconType;
export declare const IoBonfire: IconType;
export declare const IoBookOutline: IconType;
export declare const IoBookSharp: IconType;
export declare const IoBook: IconType;
export declare const IoBookmarkOutline: IconType;
export declare const IoBookmarkSharp: IconType;
export declare const IoBookmark: IconType;
export declare const IoBookmarksOutline: IconType;
export declare const IoBookmarksSharp: IconType;
export declare const IoBookmarks: IconType;
export declare const IoBowlingBallOutline: IconType;
export declare const IoBowlingBallSharp: IconType;
export declare const IoBowlingBall: IconType;
export declare const IoBriefcaseOutline: IconType;
export declare const IoBriefcaseSharp: IconType;
export declare const IoBriefcase: IconType;
export declare const IoBrowsersOutline: IconType;
export declare const IoBrowsersSharp: IconType;
export declare const IoBrowsers: IconType;
export declare const IoBrushOutline: IconType;
export declare const IoBrushSharp: IconType;
export declare const IoBrush: IconType;
export declare const IoBugOutline: IconType;
export declare const IoBugSharp: IconType;
export declare const IoBug: IconType;
export declare const IoBuildOutline: IconType;
export declare const IoBuildSharp: IconType;
export declare const IoBuild: IconType;
export declare const IoBulbOutline: IconType;
export declare const IoBulbSharp: IconType;
export declare const IoBulb: IconType;
export declare const IoBusOutline: IconType;
export declare const IoBusSharp: IconType;
export declare const IoBus: IconType;
export declare const IoBusinessOutline: IconType;
export declare const IoBusinessSharp: IconType;
export declare const IoBusiness: IconType;
export declare const IoCafeOutline: IconType;
export declare const IoCafeSharp: IconType;
export declare const IoCafe: IconType;
export declare const IoCalculatorOutline: IconType;
export declare const IoCalculatorSharp: IconType;
export declare const IoCalculator: IconType;
export declare const IoCalendarClearOutline: IconType;
export declare const IoCalendarClearSharp: IconType;
export declare const IoCalendarClear: IconType;
export declare const IoCalendarNumberOutline: IconType;
export declare const IoCalendarNumberSharp: IconType;
export declare const IoCalendarNumber: IconType;
export declare const IoCalendarOutline: IconType;
export declare const IoCalendarSharp: IconType;
export declare const IoCalendar: IconType;
export declare const IoCallOutline: IconType;
export declare const IoCallSharp: IconType;
export declare const IoCall: IconType;
export declare const IoCameraOutline: IconType;
export declare const IoCameraReverseOutline: IconType;
export declare const IoCameraReverseSharp: IconType;
export declare const IoCameraReverse: IconType;
export declare const IoCameraSharp: IconType;
export declare const IoCamera: IconType;
export declare const IoCarOutline: IconType;
export declare const IoCarSharp: IconType;
export declare const IoCarSportOutline: IconType;
export declare const IoCarSportSharp: IconType;
export declare const IoCarSport: IconType;
export declare const IoCar: IconType;
export declare const IoCardOutline: IconType;
export declare const IoCardSharp: IconType;
export declare const IoCard: IconType;
export declare const IoCaretBackCircleOutline: IconType;
export declare const IoCaretBackCircleSharp: IconType;
export declare const IoCaretBackCircle: IconType;
export declare const IoCaretBackOutline: IconType;
export declare const IoCaretBackSharp: IconType;
export declare const IoCaretBack: IconType;
export declare const IoCaretDownCircleOutline: IconType;
export declare const IoCaretDownCircleSharp: IconType;
export declare const IoCaretDownCircle: IconType;
export declare const IoCaretDownOutline: IconType;
export declare const IoCaretDownSharp: IconType;
export declare const IoCaretDown: IconType;
export declare const IoCaretForwardCircleOutline: IconType;
export declare const IoCaretForwardCircleSharp: IconType;
export declare const IoCaretForwardCircle: IconType;
export declare const IoCaretForwardOutline: IconType;
export declare const IoCaretForwardSharp: IconType;
export declare const IoCaretForward: IconType;
export declare const IoCaretUpCircleOutline: IconType;
export declare const IoCaretUpCircleSharp: IconType;
export declare const IoCaretUpCircle: IconType;
export declare const IoCaretUpOutline: IconType;
export declare const IoCaretUpSharp: IconType;
export declare const IoCaretUp: IconType;
export declare const IoCartOutline: IconType;
export declare const IoCartSharp: IconType;
export declare const IoCart: IconType;
export declare const IoCashOutline: IconType;
export declare const IoCashSharp: IconType;
export declare const IoCash: IconType;
export declare const IoCellularOutline: IconType;
export declare const IoCellularSharp: IconType;
export declare const IoCellular: IconType;
export declare const IoChatboxEllipsesOutline: IconType;
export declare const IoChatboxEllipsesSharp: IconType;
export declare const IoChatboxEllipses: IconType;
export declare const IoChatboxOutline: IconType;
export declare const IoChatboxSharp: IconType;
export declare const IoChatbox: IconType;
export declare const IoChatbubbleEllipsesOutline: IconType;
export declare const IoChatbubbleEllipsesSharp: IconType;
export declare const IoChatbubbleEllipses: IconType;
export declare const IoChatbubbleOutline: IconType;
export declare const IoChatbubbleSharp: IconType;
export declare const IoChatbubble: IconType;
export declare const IoChatbubblesOutline: IconType;
export declare const IoChatbubblesSharp: IconType;
export declare const IoChatbubbles: IconType;
export declare const IoCheckboxOutline: IconType;
export declare const IoCheckboxSharp: IconType;
export declare const IoCheckbox: IconType;
export declare const IoCheckmarkCircleOutline: IconType;
export declare const IoCheckmarkCircleSharp: IconType;
export declare const IoCheckmarkCircle: IconType;
export declare const IoCheckmarkDoneCircleOutline: IconType;
export declare const IoCheckmarkDoneCircleSharp: IconType;
export declare const IoCheckmarkDoneCircle: IconType;
export declare const IoCheckmarkDoneOutline: IconType;
export declare const IoCheckmarkDoneSharp: IconType;
export declare const IoCheckmarkDone: IconType;
export declare const IoCheckmarkOutline: IconType;
export declare const IoCheckmarkSharp: IconType;
export declare const IoCheckmark: IconType;
export declare const IoChevronBackCircleOutline: IconType;
export declare const IoChevronBackCircleSharp: IconType;
export declare const IoChevronBackCircle: IconType;
export declare const IoChevronBackOutline: IconType;
export declare const IoChevronBackSharp: IconType;
export declare const IoChevronBack: IconType;
export declare const IoChevronDownCircleOutline: IconType;
export declare const IoChevronDownCircleSharp: IconType;
export declare const IoChevronDownCircle: IconType;
export declare const IoChevronDownOutline: IconType;
export declare const IoChevronDownSharp: IconType;
export declare const IoChevronDown: IconType;
export declare const IoChevronForwardCircleOutline: IconType;
export declare const IoChevronForwardCircleSharp: IconType;
export declare const IoChevronForwardCircle: IconType;
export declare const IoChevronForwardOutline: IconType;
export declare const IoChevronForwardSharp: IconType;
export declare const IoChevronForward: IconType;
export declare const IoChevronUpCircleOutline: IconType;
export declare const IoChevronUpCircleSharp: IconType;
export declare const IoChevronUpCircle: IconType;
export declare const IoChevronUpOutline: IconType;
export declare const IoChevronUpSharp: IconType;
export declare const IoChevronUp: IconType;
export declare const IoClipboardOutline: IconType;
export declare const IoClipboardSharp: IconType;
export declare const IoClipboard: IconType;
export declare const IoCloseCircleOutline: IconType;
export declare const IoCloseCircleSharp: IconType;
export declare const IoCloseCircle: IconType;
export declare const IoCloseOutline: IconType;
export declare const IoCloseSharp: IconType;
export declare const IoClose: IconType;
export declare const IoCloudCircleOutline: IconType;
export declare const IoCloudCircleSharp: IconType;
export declare const IoCloudCircle: IconType;
export declare const IoCloudDoneOutline: IconType;
export declare const IoCloudDoneSharp: IconType;
export declare const IoCloudDone: IconType;
export declare const IoCloudDownloadOutline: IconType;
export declare const IoCloudDownloadSharp: IconType;
export declare const IoCloudDownload: IconType;
export declare const IoCloudOfflineOutline: IconType;
export declare const IoCloudOfflineSharp: IconType;
export declare const IoCloudOffline: IconType;
export declare const IoCloudOutline: IconType;
export declare const IoCloudSharp: IconType;
export declare const IoCloudUploadOutline: IconType;
export declare const IoCloudUploadSharp: IconType;
export declare const IoCloudUpload: IconType;
export declare const IoCloud: IconType;
export declare const IoCloudyNightOutline: IconType;
export declare const IoCloudyNightSharp: IconType;
export declare const IoCloudyNight: IconType;
export declare const IoCloudyOutline: IconType;
export declare const IoCloudySharp: IconType;
export declare const IoCloudy: IconType;
export declare const IoCodeDownloadOutline: IconType;
export declare const IoCodeDownloadSharp: IconType;
export declare const IoCodeDownload: IconType;
export declare const IoCodeOutline: IconType;
export declare const IoCodeSharp: IconType;
export declare const IoCodeSlashOutline: IconType;
export declare const IoCodeSlashSharp: IconType;
export declare const IoCodeSlash: IconType;
export declare const IoCodeWorkingOutline: IconType;
export declare const IoCodeWorkingSharp: IconType;
export declare const IoCodeWorking: IconType;
export declare const IoCode: IconType;
export declare const IoCogOutline: IconType;
export declare const IoCogSharp: IconType;
export declare const IoCog: IconType;
export declare const IoColorFillOutline: IconType;
export declare const IoColorFillSharp: IconType;
export declare const IoColorFill: IconType;
export declare const IoColorFilterOutline: IconType;
export declare const IoColorFilterSharp: IconType;
export declare const IoColorFilter: IconType;
export declare const IoColorPaletteOutline: IconType;
export declare const IoColorPaletteSharp: IconType;
export declare const IoColorPalette: IconType;
export declare const IoColorWandOutline: IconType;
export declare const IoColorWandSharp: IconType;
export declare const IoColorWand: IconType;
export declare const IoCompassOutline: IconType;
export declare const IoCompassSharp: IconType;
export declare const IoCompass: IconType;
export declare const IoConstructOutline: IconType;
export declare const IoConstructSharp: IconType;
export declare const IoConstruct: IconType;
export declare const IoContractOutline: IconType;
export declare const IoContractSharp: IconType;
export declare const IoContract: IconType;
export declare const IoContrastOutline: IconType;
export declare const IoContrastSharp: IconType;
export declare const IoContrast: IconType;
export declare const IoCopyOutline: IconType;
export declare const IoCopySharp: IconType;
export declare const IoCopy: IconType;
export declare const IoCreateOutline: IconType;
export declare const IoCreateSharp: IconType;
export declare const IoCreate: IconType;
export declare const IoCropOutline: IconType;
export declare const IoCropSharp: IconType;
export declare const IoCrop: IconType;
export declare const IoCubeOutline: IconType;
export declare const IoCubeSharp: IconType;
export declare const IoCube: IconType;
export declare const IoCutOutline: IconType;
export declare const IoCutSharp: IconType;
export declare const IoCut: IconType;
export declare const IoDesktopOutline: IconType;
export declare const IoDesktopSharp: IconType;
export declare const IoDesktop: IconType;
export declare const IoDiamondOutline: IconType;
export declare const IoDiamondSharp: IconType;
export declare const IoDiamond: IconType;
export declare const IoDiceOutline: IconType;
export declare const IoDiceSharp: IconType;
export declare const IoDice: IconType;
export declare const IoDiscOutline: IconType;
export declare const IoDiscSharp: IconType;
export declare const IoDisc: IconType;
export declare const IoDocumentAttachOutline: IconType;
export declare const IoDocumentAttachSharp: IconType;
export declare const IoDocumentAttach: IconType;
export declare const IoDocumentLockOutline: IconType;
export declare const IoDocumentLockSharp: IconType;
export declare const IoDocumentLock: IconType;
export declare const IoDocumentOutline: IconType;
export declare const IoDocumentSharp: IconType;
export declare const IoDocumentTextOutline: IconType;
export declare const IoDocumentTextSharp: IconType;
export declare const IoDocumentText: IconType;
export declare const IoDocument: IconType;
export declare const IoDocumentsOutline: IconType;
export declare const IoDocumentsSharp: IconType;
export declare const IoDocuments: IconType;
export declare const IoDownloadOutline: IconType;
export declare const IoDownloadSharp: IconType;
export declare const IoDownload: IconType;
export declare const IoDuplicateOutline: IconType;
export declare const IoDuplicateSharp: IconType;
export declare const IoDuplicate: IconType;
export declare const IoEarOutline: IconType;
export declare const IoEarSharp: IconType;
export declare const IoEar: IconType;
export declare const IoEarthOutline: IconType;
export declare const IoEarthSharp: IconType;
export declare const IoEarth: IconType;
export declare const IoEaselOutline: IconType;
export declare const IoEaselSharp: IconType;
export declare const IoEasel: IconType;
export declare const IoEggOutline: IconType;
export declare const IoEggSharp: IconType;
export declare const IoEgg: IconType;
export declare const IoEllipseOutline: IconType;
export declare const IoEllipseSharp: IconType;
export declare const IoEllipse: IconType;
export declare const IoEllipsisHorizontalCircleOutline: IconType;
export declare const IoEllipsisHorizontalCircleSharp: IconType;
export declare const IoEllipsisHorizontalCircle: IconType;
export declare const IoEllipsisHorizontalOutline: IconType;
export declare const IoEllipsisHorizontalSharp: IconType;
export declare const IoEllipsisHorizontal: IconType;
export declare const IoEllipsisVerticalCircleOutline: IconType;
export declare const IoEllipsisVerticalCircleSharp: IconType;
export declare const IoEllipsisVerticalCircle: IconType;
export declare const IoEllipsisVerticalOutline: IconType;
export declare const IoEllipsisVerticalSharp: IconType;
export declare const IoEllipsisVertical: IconType;
export declare const IoEnterOutline: IconType;
export declare const IoEnterSharp: IconType;
export declare const IoEnter: IconType;
export declare const IoExitOutline: IconType;
export declare const IoExitSharp: IconType;
export declare const IoExit: IconType;
export declare const IoExpandOutline: IconType;
export declare const IoExpandSharp: IconType;
export declare const IoExpand: IconType;
export declare const IoExtensionPuzzleOutline: IconType;
export declare const IoExtensionPuzzleSharp: IconType;
export declare const IoExtensionPuzzle: IconType;
export declare const IoEyeOffOutline: IconType;
export declare const IoEyeOffSharp: IconType;
export declare const IoEyeOff: IconType;
export declare const IoEyeOutline: IconType;
export declare const IoEyeSharp: IconType;
export declare const IoEye: IconType;
export declare const IoEyedropOutline: IconType;
export declare const IoEyedropSharp: IconType;
export declare const IoEyedrop: IconType;
export declare const IoFastFoodOutline: IconType;
export declare const IoFastFoodSharp: IconType;
export declare const IoFastFood: IconType;
export declare const IoFemaleOutline: IconType;
export declare const IoFemaleSharp: IconType;
export declare const IoFemale: IconType;
export declare const IoFileTrayFullOutline: IconType;
export declare const IoFileTrayFullSharp: IconType;
export declare const IoFileTrayFull: IconType;
export declare const IoFileTrayOutline: IconType;
export declare const IoFileTraySharp: IconType;
export declare const IoFileTrayStackedOutline: IconType;
export declare const IoFileTrayStackedSharp: IconType;
export declare const IoFileTrayStacked: IconType;
export declare const IoFileTray: IconType;
export declare const IoFilmOutline: IconType;
export declare const IoFilmSharp: IconType;
export declare const IoFilm: IconType;
export declare const IoFilterCircleOutline: IconType;
export declare const IoFilterCircleSharp: IconType;
export declare const IoFilterCircle: IconType;
export declare const IoFilterOutline: IconType;
export declare const IoFilterSharp: IconType;
export declare const IoFilter: IconType;
export declare const IoFingerPrintOutline: IconType;
export declare const IoFingerPrintSharp: IconType;
export declare const IoFingerPrint: IconType;
export declare const IoFishOutline: IconType;
export declare const IoFishSharp: IconType;
export declare const IoFish: IconType;
export declare const IoFitnessOutline: IconType;
export declare const IoFitnessSharp: IconType;
export declare const IoFitness: IconType;
export declare const IoFlagOutline: IconType;
export declare const IoFlagSharp: IconType;
export declare const IoFlag: IconType;
export declare const IoFlameOutline: IconType;
export declare const IoFlameSharp: IconType;
export declare const IoFlame: IconType;
export declare const IoFlashOffOutline: IconType;
export declare const IoFlashOffSharp: IconType;
export declare const IoFlashOff: IconType;
export declare const IoFlashOutline: IconType;
export declare const IoFlashSharp: IconType;
export declare const IoFlash: IconType;
export declare const IoFlashlightOutline: IconType;
export declare const IoFlashlightSharp: IconType;
export declare const IoFlashlight: IconType;
export declare const IoFlaskOutline: IconType;
export declare const IoFlaskSharp: IconType;
export declare const IoFlask: IconType;
export declare const IoFlowerOutline: IconType;
export declare const IoFlowerSharp: IconType;
export declare const IoFlower: IconType;
export declare const IoFolderOpenOutline: IconType;
export declare const IoFolderOpenSharp: IconType;
export declare const IoFolderOpen: IconType;
export declare const IoFolderOutline: IconType;
export declare const IoFolderSharp: IconType;
export declare const IoFolder: IconType;
export declare const IoFootballOutline: IconType;
export declare const IoFootballSharp: IconType;
export declare const IoFootball: IconType;
export declare const IoFootstepsOutline: IconType;
export declare const IoFootstepsSharp: IconType;
export declare const IoFootsteps: IconType;
export declare const IoFunnelOutline: IconType;
export declare const IoFunnelSharp: IconType;
export declare const IoFunnel: IconType;
export declare const IoGameControllerOutline: IconType;
export declare const IoGameControllerSharp: IconType;
export declare const IoGameController: IconType;
export declare const IoGiftOutline: IconType;
export declare const IoGiftSharp: IconType;
export declare const IoGift: IconType;
export declare const IoGitBranchOutline: IconType;
export declare const IoGitBranchSharp: IconType;
export declare const IoGitBranch: IconType;
export declare const IoGitCommitOutline: IconType;
export declare const IoGitCommitSharp: IconType;
export declare const IoGitCommit: IconType;
export declare const IoGitCompareOutline: IconType;
export declare const IoGitCompareSharp: IconType;
export declare const IoGitCompare: IconType;
export declare const IoGitMergeOutline: IconType;
export declare const IoGitMergeSharp: IconType;
export declare const IoGitMerge: IconType;
export declare const IoGitNetworkOutline: IconType;
export declare const IoGitNetworkSharp: IconType;
export declare const IoGitNetwork: IconType;
export declare const IoGitPullRequestOutline: IconType;
export declare const IoGitPullRequestSharp: IconType;
export declare const IoGitPullRequest: IconType;
export declare const IoGlassesOutline: IconType;
export declare const IoGlassesSharp: IconType;
export declare const IoGlasses: IconType;
export declare const IoGlobeOutline: IconType;
export declare const IoGlobeSharp: IconType;
export declare const IoGlobe: IconType;
export declare const IoGolfOutline: IconType;
export declare const IoGolfSharp: IconType;
export declare const IoGolf: IconType;
export declare const IoGridOutline: IconType;
export declare const IoGridSharp: IconType;
export declare const IoGrid: IconType;
export declare const IoHammerOutline: IconType;
export declare const IoHammerSharp: IconType;
export declare const IoHammer: IconType;
export declare const IoHandLeftOutline: IconType;
export declare const IoHandLeftSharp: IconType;
export declare const IoHandLeft: IconType;
export declare const IoHandRightOutline: IconType;
export declare const IoHandRightSharp: IconType;
export declare const IoHandRight: IconType;
export declare const IoHappyOutline: IconType;
export declare const IoHappySharp: IconType;
export declare const IoHappy: IconType;
export declare const IoHardwareChipOutline: IconType;
export declare const IoHardwareChipSharp: IconType;
export declare const IoHardwareChip: IconType;
export declare const IoHeadsetOutline: IconType;
export declare const IoHeadsetSharp: IconType;
export declare const IoHeadset: IconType;
export declare const IoHeartCircleOutline: IconType;
export declare const IoHeartCircleSharp: IconType;
export declare const IoHeartCircle: IconType;
export declare const IoHeartDislikeCircleOutline: IconType;
export declare const IoHeartDislikeCircleSharp: IconType;
export declare const IoHeartDislikeCircle: IconType;
export declare const IoHeartDislikeOutline: IconType;
export declare const IoHeartDislikeSharp: IconType;
export declare const IoHeartDislike: IconType;
export declare const IoHeartHalfOutline: IconType;
export declare const IoHeartHalfSharp: IconType;
export declare const IoHeartHalf: IconType;
export declare const IoHeartOutline: IconType;
export declare const IoHeartSharp: IconType;
export declare const IoHeart: IconType;
export declare const IoHelpBuoyOutline: IconType;
export declare const IoHelpBuoySharp: IconType;
export declare const IoHelpBuoy: IconType;
export declare const IoHelpCircleOutline: IconType;
export declare const IoHelpCircleSharp: IconType;
export declare const IoHelpCircle: IconType;
export declare const IoHelpOutline: IconType;
export declare const IoHelpSharp: IconType;
export declare const IoHelp: IconType;
export declare const IoHomeOutline: IconType;
export declare const IoHomeSharp: IconType;
export declare const IoHome: IconType;
export declare const IoHourglassOutline: IconType;
export declare const IoHourglassSharp: IconType;
export declare const IoHourglass: IconType;
export declare const IoIceCreamOutline: IconType;
export declare const IoIceCreamSharp: IconType;
export declare const IoIceCream: IconType;
export declare const IoIdCardOutline: IconType;
export declare const IoIdCardSharp: IconType;
export declare const IoIdCard: IconType;
export declare const IoImageOutline: IconType;
export declare const IoImageSharp: IconType;
export declare const IoImage: IconType;
export declare const IoImagesOutline: IconType;
export declare const IoImagesSharp: IconType;
export declare const IoImages: IconType;
export declare const IoInfiniteOutline: IconType;
export declare const IoInfiniteSharp: IconType;
export declare const IoInfinite: IconType;
export declare const IoInformationCircleOutline: IconType;
export declare const IoInformationCircleSharp: IconType;
export declare const IoInformationCircle: IconType;
export declare const IoInformationOutline: IconType;
export declare const IoInformationSharp: IconType;
export declare const IoInformation: IconType;
export declare const IoInvertModeOutline: IconType;
export declare const IoInvertModeSharp: IconType;
export declare const IoInvertMode: IconType;
export declare const IoJournalOutline: IconType;
export declare const IoJournalSharp: IconType;
export declare const IoJournal: IconType;
export declare const IoKeyOutline: IconType;
export declare const IoKeySharp: IconType;
export declare const IoKey: IconType;
export declare const IoKeypadOutline: IconType;
export declare const IoKeypadSharp: IconType;
export declare const IoKeypad: IconType;
export declare const IoLanguageOutline: IconType;
export declare const IoLanguageSharp: IconType;
export declare const IoLanguage: IconType;
export declare const IoLaptopOutline: IconType;
export declare const IoLaptopSharp: IconType;
export declare const IoLaptop: IconType;
export declare const IoLayersOutline: IconType;
export declare const IoLayersSharp: IconType;
export declare const IoLayers: IconType;
export declare const IoLeafOutline: IconType;
export declare const IoLeafSharp: IconType;
export declare const IoLeaf: IconType;
export declare const IoLibraryOutline: IconType;
export declare const IoLibrarySharp: IconType;
export declare const IoLibrary: IconType;
export declare const IoLinkOutline: IconType;
export declare const IoLinkSharp: IconType;
export declare const IoLink: IconType;
export declare const IoListCircleOutline: IconType;
export declare const IoListCircleSharp: IconType;
export declare const IoListCircle: IconType;
export declare const IoListOutline: IconType;
export declare const IoListSharp: IconType;
export declare const IoList: IconType;
export declare const IoLocateOutline: IconType;
export declare const IoLocateSharp: IconType;
export declare const IoLocate: IconType;
export declare const IoLocationOutline: IconType;
export declare const IoLocationSharp: IconType;
export declare const IoLocation: IconType;
export declare const IoLockClosedOutline: IconType;
export declare const IoLockClosedSharp: IconType;
export declare const IoLockClosed: IconType;
export declare const IoLockOpenOutline: IconType;
export declare const IoLockOpenSharp: IconType;
export declare const IoLockOpen: IconType;
export declare const IoLogInOutline: IconType;
export declare const IoLogInSharp: IconType;
export declare const IoLogIn: IconType;
export declare const IoLogOutOutline: IconType;
export declare const IoLogOutSharp: IconType;
export declare const IoLogOut: IconType;
export declare const IoLogoAlipay: IconType;
export declare const IoLogoAmazon: IconType;
export declare const IoLogoAmplify: IconType;
export declare const IoLogoAndroid: IconType;
export declare const IoLogoAngular: IconType;
export declare const IoLogoAppleAppstore: IconType;
export declare const IoLogoAppleAr: IconType;
export declare const IoLogoApple: IconType;
export declare const IoLogoBehance: IconType;
export declare const IoLogoBitbucket: IconType;
export declare const IoLogoBitcoin: IconType;
export declare const IoLogoBuffer: IconType;
export declare const IoLogoCapacitor: IconType;
export declare const IoLogoChrome: IconType;
export declare const IoLogoClosedCaptioning: IconType;
export declare const IoLogoCodepen: IconType;
export declare const IoLogoCss3: IconType;
export declare const IoLogoDesignernews: IconType;
export declare const IoLogoDeviantart: IconType;
export declare const IoLogoDiscord: IconType;
export declare const IoLogoDocker: IconType;
export declare const IoLogoDribbble: IconType;
export declare const IoLogoDropbox: IconType;
export declare const IoLogoEdge: IconType;
export declare const IoLogoElectron: IconType;
export declare const IoLogoEuro: IconType;
export declare const IoLogoFacebook: IconType;
export declare const IoLogoFigma: IconType;
export declare const IoLogoFirebase: IconType;
export declare const IoLogoFirefox: IconType;
export declare const IoLogoFlickr: IconType;
export declare const IoLogoFoursquare: IconType;
export declare const IoLogoGithub: IconType;
export declare const IoLogoGitlab: IconType;
export declare const IoLogoGooglePlaystore: IconType;
export declare const IoLogoGoogle: IconType;
export declare const IoLogoHackernews: IconType;
export declare const IoLogoHtml5: IconType;
export declare const IoLogoInstagram: IconType;
export declare const IoLogoIonic: IconType;
export declare const IoLogoIonitron: IconType;
export declare const IoLogoJavascript: IconType;
export declare const IoLogoLaravel: IconType;
export declare const IoLogoLinkedin: IconType;
export declare const IoLogoMarkdown: IconType;
export declare const IoLogoMastodon: IconType;
export declare const IoLogoMedium: IconType;
export declare const IoLogoMicrosoft: IconType;
export declare const IoLogoNoSmoking: IconType;
export declare const IoLogoNodejs: IconType;
export declare const IoLogoNpm: IconType;
export declare const IoLogoOctocat: IconType;
export declare const IoLogoPaypal: IconType;
export declare const IoLogoPinterest: IconType;
export declare const IoLogoPlaystation: IconType;
export declare const IoLogoPwa: IconType;
export declare const IoLogoPython: IconType;
export declare const IoLogoReact: IconType;
export declare const IoLogoReddit: IconType;
export declare const IoLogoRss: IconType;
export declare const IoLogoSass: IconType;
export declare const IoLogoSkype: IconType;
export declare const IoLogoSlack: IconType;
export declare const IoLogoSnapchat: IconType;
export declare const IoLogoSoundcloud: IconType;
export declare const IoLogoStackoverflow: IconType;
export declare const IoLogoSteam: IconType;
export declare const IoLogoStencil: IconType;
export declare const IoLogoTableau: IconType;
export declare const IoLogoTiktok: IconType;
export declare const IoLogoTumblr: IconType;
export declare const IoLogoTux: IconType;
export declare const IoLogoTwitch: IconType;
export declare const IoLogoTwitter: IconType;
export declare const IoLogoUsd: IconType;
export declare const IoLogoVenmo: IconType;
export declare const IoLogoVercel: IconType;
export declare const IoLogoVimeo: IconType;
export declare const IoLogoVk: IconType;
export declare const IoLogoVue: IconType;
export declare const IoLogoWebComponent: IconType;
export declare const IoLogoWechat: IconType;
export declare const IoLogoWhatsapp: IconType;
export declare const IoLogoWindows: IconType;
export declare const IoLogoWordpress: IconType;
export declare const IoLogoXbox: IconType;
export declare const IoLogoXing: IconType;
export declare const IoLogoYahoo: IconType;
export declare const IoLogoYen: IconType;
export declare const IoLogoYoutube: IconType;
export declare const IoMagnetOutline: IconType;
export declare const IoMagnetSharp: IconType;
export declare const IoMagnet: IconType;
export declare const IoMailOpenOutline: IconType;
export declare const IoMailOpenSharp: IconType;
export declare const IoMailOpen: IconType;
export declare const IoMailOutline: IconType;
export declare const IoMailSharp: IconType;
export declare const IoMailUnreadOutline: IconType;
export declare const IoMailUnreadSharp: IconType;
export declare const IoMailUnread: IconType;
export declare const IoMail: IconType;
export declare const IoMaleFemaleOutline: IconType;
export declare const IoMaleFemaleSharp: IconType;
export declare const IoMaleFemale: IconType;
export declare const IoMaleOutline: IconType;
export declare const IoMaleSharp: IconType;
export declare const IoMale: IconType;
export declare const IoManOutline: IconType;
export declare const IoManSharp: IconType;
export declare const IoMan: IconType;
export declare const IoMapOutline: IconType;
export declare const IoMapSharp: IconType;
export declare const IoMap: IconType;
export declare const IoMedalOutline: IconType;
export declare const IoMedalSharp: IconType;
export declare const IoMedal: IconType;
export declare const IoMedicalOutline: IconType;
export declare const IoMedicalSharp: IconType;
export declare const IoMedical: IconType;
export declare const IoMedkitOutline: IconType;
export declare const IoMedkitSharp: IconType;
export declare const IoMedkit: IconType;
export declare const IoMegaphoneOutline: IconType;
export declare const IoMegaphoneSharp: IconType;
export declare const IoMegaphone: IconType;
export declare const IoMenuOutline: IconType;
export declare const IoMenuSharp: IconType;
export declare const IoMenu: IconType;
export declare const IoMicCircleOutline: IconType;
export declare const IoMicCircleSharp: IconType;
export declare const IoMicCircle: IconType;
export declare const IoMicOffCircleOutline: IconType;
export declare const IoMicOffCircleSharp: IconType;
export declare const IoMicOffCircle: IconType;
export declare const IoMicOffOutline: IconType;
export declare const IoMicOffSharp: IconType;
export declare const IoMicOff: IconType;
export declare const IoMicOutline: IconType;
export declare const IoMicSharp: IconType;
export declare const IoMic: IconType;
export declare const IoMoonOutline: IconType;
export declare const IoMoonSharp: IconType;
export declare const IoMoon: IconType;
export declare const IoMoveOutline: IconType;
export declare const IoMoveSharp: IconType;
export declare const IoMove: IconType;
export declare const IoMusicalNoteOutline: IconType;
export declare const IoMusicalNoteSharp: IconType;
export declare const IoMusicalNote: IconType;
export declare const IoMusicalNotesOutline: IconType;
export declare const IoMusicalNotesSharp: IconType;
export declare const IoMusicalNotes: IconType;
export declare const IoNavigateCircleOutline: IconType;
export declare const IoNavigateCircleSharp: IconType;
export declare const IoNavigateCircle: IconType;
export declare const IoNavigateOutline: IconType;
export declare const IoNavigateSharp: IconType;
export declare const IoNavigate: IconType;
export declare const IoNewspaperOutline: IconType;
export declare const IoNewspaperSharp: IconType;
export declare const IoNewspaper: IconType;
export declare const IoNotificationsCircleOutline: IconType;
export declare const IoNotificationsCircleSharp: IconType;
export declare const IoNotificationsCircle: IconType;
export declare const IoNotificationsOffCircleOutline: IconType;
export declare const IoNotificationsOffCircleSharp: IconType;
export declare const IoNotificationsOffCircle: IconType;
export declare const IoNotificationsOffOutline: IconType;
export declare const IoNotificationsOffSharp: IconType;
export declare const IoNotificationsOff: IconType;
export declare const IoNotificationsOutline: IconType;
export declare const IoNotificationsSharp: IconType;
export declare const IoNotifications: IconType;
export declare const IoNuclearOutline: IconType;
export declare const IoNuclearSharp: IconType;
export declare const IoNuclear: IconType;
export declare const IoNutritionOutline: IconType;
export declare const IoNutritionSharp: IconType;
export declare const IoNutrition: IconType;
export declare const IoOpenOutline: IconType;
export declare const IoOpenSharp: IconType;
export declare const IoOpen: IconType;
export declare const IoOptionsOutline: IconType;
export declare const IoOptionsSharp: IconType;
export declare const IoOptions: IconType;
export declare const IoPaperPlaneOutline: IconType;
export declare const IoPaperPlaneSharp: IconType;
export declare const IoPaperPlane: IconType;
export declare const IoPartlySunnyOutline: IconType;
export declare const IoPartlySunnySharp: IconType;
export declare const IoPartlySunny: IconType;
export declare const IoPauseCircleOutline: IconType;
export declare const IoPauseCircleSharp: IconType;
export declare const IoPauseCircle: IconType;
export declare const IoPauseOutline: IconType;
export declare const IoPauseSharp: IconType;
export declare const IoPause: IconType;
export declare const IoPawOutline: IconType;
export declare const IoPawSharp: IconType;
export declare const IoPaw: IconType;
export declare const IoPencilOutline: IconType;
export declare const IoPencilSharp: IconType;
export declare const IoPencil: IconType;
export declare const IoPeopleCircleOutline: IconType;
export declare const IoPeopleCircleSharp: IconType;
export declare const IoPeopleCircle: IconType;
export declare const IoPeopleOutline: IconType;
export declare const IoPeopleSharp: IconType;
export declare const IoPeople: IconType;
export declare const IoPersonAddOutline: IconType;
export declare const IoPersonAddSharp: IconType;
export declare const IoPersonAdd: IconType;
export declare const IoPersonCircleOutline: IconType;
export declare const IoPersonCircleSharp: IconType;
export declare const IoPersonCircle: IconType;
export declare const IoPersonOutline: IconType;
export declare const IoPersonRemoveOutline: IconType;
export declare const IoPersonRemoveSharp: IconType;
export declare const IoPersonRemove: IconType;
export declare const IoPersonSharp: IconType;
export declare const IoPerson: IconType;
export declare const IoPhoneLandscapeOutline: IconType;
export declare const IoPhoneLandscapeSharp: IconType;
export declare const IoPhoneLandscape: IconType;
export declare const IoPhonePortraitOutline: IconType;
export declare const IoPhonePortraitSharp: IconType;
export declare const IoPhonePortrait: IconType;
export declare const IoPieChartOutline: IconType;
export declare const IoPieChartSharp: IconType;
export declare const IoPieChart: IconType;
export declare const IoPinOutline: IconType;
export declare const IoPinSharp: IconType;
export declare const IoPin: IconType;
export declare const IoPintOutline: IconType;
export declare const IoPintSharp: IconType;
export declare const IoPint: IconType;
export declare const IoPizzaOutline: IconType;
export declare const IoPizzaSharp: IconType;
export declare const IoPizza: IconType;
export declare const IoPlanetOutline: IconType;
export declare const IoPlanetSharp: IconType;
export declare const IoPlanet: IconType;
export declare const IoPlayBackCircleOutline: IconType;
export declare const IoPlayBackCircleSharp: IconType;
export declare const IoPlayBackCircle: IconType;
export declare const IoPlayBackOutline: IconType;
export declare const IoPlayBackSharp: IconType;
export declare const IoPlayBack: IconType;
export declare const IoPlayCircleOutline: IconType;
export declare const IoPlayCircleSharp: IconType;
export declare const IoPlayCircle: IconType;
export declare const IoPlayForwardCircleOutline: IconType;
export declare const IoPlayForwardCircleSharp: IconType;
export declare const IoPlayForwardCircle: IconType;
export declare const IoPlayForwardOutline: IconType;
export declare const IoPlayForwardSharp: IconType;
export declare const IoPlayForward: IconType;
export declare const IoPlayOutline: IconType;
export declare const IoPlaySharp: IconType;
export declare const IoPlaySkipBackCircleOutline: IconType;
export declare const IoPlaySkipBackCircleSharp: IconType;
export declare const IoPlaySkipBackCircle: IconType;
export declare const IoPlaySkipBackOutline: IconType;
export declare const IoPlaySkipBackSharp: IconType;
export declare const IoPlaySkipBack: IconType;
export declare const IoPlaySkipForwardCircleOutline: IconType;
export declare const IoPlaySkipForwardCircleSharp: IconType;
export declare const IoPlaySkipForwardCircle: IconType;
export declare const IoPlaySkipForwardOutline: IconType;
export declare const IoPlaySkipForwardSharp: IconType;
export declare const IoPlaySkipForward: IconType;
export declare const IoPlay: IconType;
export declare const IoPodiumOutline: IconType;
export declare const IoPodiumSharp: IconType;
export declare const IoPodium: IconType;
export declare const IoPowerOutline: IconType;
export declare const IoPowerSharp: IconType;
export declare const IoPower: IconType;
export declare const IoPricetagOutline: IconType;
export declare const IoPricetagSharp: IconType;
export declare const IoPricetag: IconType;
export declare const IoPricetagsOutline: IconType;
export declare const IoPricetagsSharp: IconType;
export declare const IoPricetags: IconType;
export declare const IoPrintOutline: IconType;
export declare const IoPrintSharp: IconType;
export declare const IoPrint: IconType;
export declare const IoPrismOutline: IconType;
export declare const IoPrismSharp: IconType;
export declare const IoPrism: IconType;
export declare const IoPulseOutline: IconType;
export declare const IoPulseSharp: IconType;
export declare const IoPulse: IconType;
export declare const IoPushOutline: IconType;
export declare const IoPushSharp: IconType;
export declare const IoPush: IconType;
export declare const IoQrCodeOutline: IconType;
export declare const IoQrCodeSharp: IconType;
export declare const IoQrCode: IconType;
export declare const IoRadioButtonOffOutline: IconType;
export declare const IoRadioButtonOffSharp: IconType;
export declare const IoRadioButtonOff: IconType;
export declare const IoRadioButtonOnOutline: IconType;
export declare const IoRadioButtonOnSharp: IconType;
export declare const IoRadioButtonOn: IconType;
export declare const IoRadioOutline: IconType;
export declare const IoRadioSharp: IconType;
export declare const IoRadio: IconType;
export declare const IoRainyOutline: IconType;
export declare const IoRainySharp: IconType;
export declare const IoRainy: IconType;
export declare const IoReaderOutline: IconType;
export declare const IoReaderSharp: IconType;
export declare const IoReader: IconType;
export declare const IoReceiptOutline: IconType;
export declare const IoReceiptSharp: IconType;
export declare const IoReceipt: IconType;
export declare const IoRecordingOutline: IconType;
export declare const IoRecordingSharp: IconType;
export declare const IoRecording: IconType;
export declare const IoRefreshCircleOutline: IconType;
export declare const IoRefreshCircleSharp: IconType;
export declare const IoRefreshCircle: IconType;
export declare const IoRefreshOutline: IconType;
export declare const IoRefreshSharp: IconType;
export declare const IoRefresh: IconType;
export declare const IoReloadCircleOutline: IconType;
export declare const IoReloadCircleSharp: IconType;
export declare const IoReloadCircle: IconType;
export declare const IoReloadOutline: IconType;
export declare const IoReloadSharp: IconType;
export declare const IoReload: IconType;
export declare const IoRemoveCircleOutline: IconType;
export declare const IoRemoveCircleSharp: IconType;
export declare const IoRemoveCircle: IconType;
export declare const IoRemoveOutline: IconType;
export declare const IoRemoveSharp: IconType;
export declare const IoRemove: IconType;
export declare const IoReorderFourOutline: IconType;
export declare const IoReorderFourSharp: IconType;
export declare const IoReorderFour: IconType;
export declare const IoReorderThreeOutline: IconType;
export declare const IoReorderThreeSharp: IconType;
export declare const IoReorderThree: IconType;
export declare const IoReorderTwoOutline: IconType;
export declare const IoReorderTwoSharp: IconType;
export declare const IoReorderTwo: IconType;
export declare const IoRepeatOutline: IconType;
export declare const IoRepeatSharp: IconType;
export declare const IoRepeat: IconType;
export declare const IoResizeOutline: IconType;
export declare const IoResizeSharp: IconType;
export declare const IoResize: IconType;
export declare const IoRestaurantOutline: IconType;
export declare const IoRestaurantSharp: IconType;
export declare const IoRestaurant: IconType;
export declare const IoReturnDownBackOutline: IconType;
export declare const IoReturnDownBackSharp: IconType;
export declare const IoReturnDownBack: IconType;
export declare const IoReturnDownForwardOutline: IconType;
export declare const IoReturnDownForwardSharp: IconType;
export declare const IoReturnDownForward: IconType;
export declare const IoReturnUpBackOutline: IconType;
export declare const IoReturnUpBackSharp: IconType;
export declare const IoReturnUpBack: IconType;
export declare const IoReturnUpForwardOutline: IconType;
export declare const IoReturnUpForwardSharp: IconType;
export declare const IoReturnUpForward: IconType;
export declare const IoRibbonOutline: IconType;
export declare const IoRibbonSharp: IconType;
export declare const IoRibbon: IconType;
export declare const IoRocketOutline: IconType;
export declare const IoRocketSharp: IconType;
export declare const IoRocket: IconType;
export declare const IoRoseOutline: IconType;
export declare const IoRoseSharp: IconType;
export declare const IoRose: IconType;
export declare const IoSadOutline: IconType;
export declare const IoSadSharp: IconType;
export declare const IoSad: IconType;
export declare const IoSaveOutline: IconType;
export declare const IoSaveSharp: IconType;
export declare const IoSave: IconType;
export declare const IoScaleOutline: IconType;
export declare const IoScaleSharp: IconType;
export declare const IoScale: IconType;
export declare const IoScanCircleOutline: IconType;
export declare const IoScanCircleSharp: IconType;
export declare const IoScanCircle: IconType;
export declare const IoScanOutline: IconType;
export declare const IoScanSharp: IconType;
export declare const IoScan: IconType;
export declare const IoSchoolOutline: IconType;
export declare const IoSchoolSharp: IconType;
export declare const IoSchool: IconType;
export declare const IoSearchCircleOutline: IconType;
export declare const IoSearchCircleSharp: IconType;
export declare const IoSearchCircle: IconType;
export declare const IoSearchOutline: IconType;
export declare const IoSearchSharp: IconType;
export declare const IoSearch: IconType;
export declare const IoSendOutline: IconType;
export declare const IoSendSharp: IconType;
export declare const IoSend: IconType;
export declare const IoServerOutline: IconType;
export declare const IoServerSharp: IconType;
export declare const IoServer: IconType;
export declare const IoSettingsOutline: IconType;
export declare const IoSettingsSharp: IconType;
export declare const IoSettings: IconType;
export declare const IoShapesOutline: IconType;
export declare const IoShapesSharp: IconType;
export declare const IoShapes: IconType;
export declare const IoShareOutline: IconType;
export declare const IoShareSharp: IconType;
export declare const IoShareSocialOutline: IconType;
export declare const IoShareSocialSharp: IconType;
export declare const IoShareSocial: IconType;
export declare const IoShare: IconType;
export declare const IoShieldCheckmarkOutline: IconType;
export declare const IoShieldCheckmarkSharp: IconType;
export declare const IoShieldCheckmark: IconType;
export declare const IoShieldHalfOutline: IconType;
export declare const IoShieldHalfSharp: IconType;
export declare const IoShieldHalf: IconType;
export declare const IoShieldOutline: IconType;
export declare const IoShieldSharp: IconType;
export declare const IoShield: IconType;
export declare const IoShirtOutline: IconType;
export declare const IoShirtSharp: IconType;
export declare const IoShirt: IconType;
export declare const IoShuffleOutline: IconType;
export declare const IoShuffleSharp: IconType;
export declare const IoShuffle: IconType;
export declare const IoSkullOutline: IconType;
export declare const IoSkullSharp: IconType;
export declare const IoSkull: IconType;
export declare const IoSnowOutline: IconType;
export declare const IoSnowSharp: IconType;
export declare const IoSnow: IconType;
export declare const IoSparklesOutline: IconType;
export declare const IoSparklesSharp: IconType;
export declare const IoSparkles: IconType;
export declare const IoSpeedometerOutline: IconType;
export declare const IoSpeedometerSharp: IconType;
export declare const IoSpeedometer: IconType;
export declare const IoSquareOutline: IconType;
export declare const IoSquareSharp: IconType;
export declare const IoSquare: IconType;
export declare const IoStarHalfOutline: IconType;
export declare const IoStarHalfSharp: IconType;
export declare const IoStarHalf: IconType;
export declare const IoStarOutline: IconType;
export declare const IoStarSharp: IconType;
export declare const IoStar: IconType;
export declare const IoStatsChartOutline: IconType;
export declare const IoStatsChartSharp: IconType;
export declare const IoStatsChart: IconType;
export declare const IoStopCircleOutline: IconType;
export declare const IoStopCircleSharp: IconType;
export declare const IoStopCircle: IconType;
export declare const IoStopOutline: IconType;
export declare const IoStopSharp: IconType;
export declare const IoStop: IconType;
export declare const IoStopwatchOutline: IconType;
export declare const IoStopwatchSharp: IconType;
export declare const IoStopwatch: IconType;
export declare const IoStorefrontOutline: IconType;
export declare const IoStorefrontSharp: IconType;
export declare const IoStorefront: IconType;
export declare const IoSubwayOutline: IconType;
export declare const IoSubwaySharp: IconType;
export declare const IoSubway: IconType;
export declare const IoSunnyOutline: IconType;
export declare const IoSunnySharp: IconType;
export declare const IoSunny: IconType;
export declare const IoSwapHorizontalOutline: IconType;
export declare const IoSwapHorizontalSharp: IconType;
export declare const IoSwapHorizontal: IconType;
export declare const IoSwapVerticalOutline: IconType;
export declare const IoSwapVerticalSharp: IconType;
export declare const IoSwapVertical: IconType;
export declare const IoSyncCircleOutline: IconType;
export declare const IoSyncCircleSharp: IconType;
export declare const IoSyncCircle: IconType;
export declare const IoSyncOutline: IconType;
export declare const IoSyncSharp: IconType;
export declare const IoSync: IconType;
export declare const IoTabletLandscapeOutline: IconType;
export declare const IoTabletLandscapeSharp: IconType;
export declare const IoTabletLandscape: IconType;
export declare const IoTabletPortraitOutline: IconType;
export declare const IoTabletPortraitSharp: IconType;
export declare const IoTabletPortrait: IconType;
export declare const IoTelescopeOutline: IconType;
export declare const IoTelescopeSharp: IconType;
export declare const IoTelescope: IconType;
export declare const IoTennisballOutline: IconType;
export declare const IoTennisballSharp: IconType;
export declare const IoTennisball: IconType;
export declare const IoTerminalOutline: IconType;
export declare const IoTerminalSharp: IconType;
export declare const IoTerminal: IconType;
export declare const IoTextOutline: IconType;
export declare const IoTextSharp: IconType;
export declare const IoText: IconType;
export declare const IoThermometerOutline: IconType;
export declare const IoThermometerSharp: IconType;
export declare const IoThermometer: IconType;
export declare const IoThumbsDownOutline: IconType;
export declare const IoThumbsDownSharp: IconType;
export declare const IoThumbsDown: IconType;
export declare const IoThumbsUpOutline: IconType;
export declare const IoThumbsUpSharp: IconType;
export declare const IoThumbsUp: IconType;
export declare const IoThunderstormOutline: IconType;
export declare const IoThunderstormSharp: IconType;
export declare const IoThunderstorm: IconType;
export declare const IoTicketOutline: IconType;
export declare const IoTicketSharp: IconType;
export declare const IoTicket: IconType;
export declare const IoTimeOutline: IconType;
export declare const IoTimeSharp: IconType;
export declare const IoTime: IconType;
export declare const IoTimerOutline: IconType;
export declare const IoTimerSharp: IconType;
export declare const IoTimer: IconType;
export declare const IoTodayOutline: IconType;
export declare const IoTodaySharp: IconType;
export declare const IoToday: IconType;
export declare const IoToggleOutline: IconType;
export declare const IoToggleSharp: IconType;
export declare const IoToggle: IconType;
export declare const IoTrailSignOutline: IconType;
export declare const IoTrailSignSharp: IconType;
export declare const IoTrailSign: IconType;
export declare const IoTrainOutline: IconType;
export declare const IoTrainSharp: IconType;
export declare const IoTrain: IconType;
export declare const IoTransgenderOutline: IconType;
export declare const IoTransgenderSharp: IconType;
export declare const IoTransgender: IconType;
export declare const IoTrashBinOutline: IconType;
export declare const IoTrashBinSharp: IconType;
export declare const IoTrashBin: IconType;
export declare const IoTrashOutline: IconType;
export declare const IoTrashSharp: IconType;
export declare const IoTrash: IconType;
export declare const IoTrendingDownOutline: IconType;
export declare const IoTrendingDownSharp: IconType;
export declare const IoTrendingDown: IconType;
export declare const IoTrendingUpOutline: IconType;
export declare const IoTrendingUpSharp: IconType;
export declare const IoTrendingUp: IconType;
export declare const IoTriangleOutline: IconType;
export declare const IoTriangleSharp: IconType;
export declare const IoTriangle: IconType;
export declare const IoTrophyOutline: IconType;
export declare const IoTrophySharp: IconType;
export declare const IoTrophy: IconType;
export declare const IoTvOutline: IconType;
export declare const IoTvSharp: IconType;
export declare const IoTv: IconType;
export declare const IoUmbrellaOutline: IconType;
export declare const IoUmbrellaSharp: IconType;
export declare const IoUmbrella: IconType;
export declare const IoUnlinkOutline: IconType;
export declare const IoUnlinkSharp: IconType;
export declare const IoUnlink: IconType;
export declare const IoVideocamOffOutline: IconType;
export declare const IoVideocamOffSharp: IconType;
export declare const IoVideocamOff: IconType;
export declare const IoVideocamOutline: IconType;
export declare const IoVideocamSharp: IconType;
export declare const IoVideocam: IconType;
export declare const IoVolumeHighOutline: IconType;
export declare const IoVolumeHighSharp: IconType;
export declare const IoVolumeHigh: IconType;
export declare const IoVolumeLowOutline: IconType;
export declare const IoVolumeLowSharp: IconType;
export declare const IoVolumeLow: IconType;
export declare const IoVolumeMediumOutline: IconType;
export declare const IoVolumeMediumSharp: IconType;
export declare const IoVolumeMedium: IconType;
export declare const IoVolumeMuteOutline: IconType;
export declare const IoVolumeMuteSharp: IconType;
export declare const IoVolumeMute: IconType;
export declare const IoVolumeOffOutline: IconType;
export declare const IoVolumeOffSharp: IconType;
export declare const IoVolumeOff: IconType;
export declare const IoWalkOutline: IconType;
export declare const IoWalkSharp: IconType;
export declare const IoWalk: IconType;
export declare const IoWalletOutline: IconType;
export declare const IoWalletSharp: IconType;
export declare const IoWallet: IconType;
export declare const IoWarningOutline: IconType;
export declare const IoWarningSharp: IconType;
export declare const IoWarning: IconType;
export declare const IoWatchOutline: IconType;
export declare const IoWatchSharp: IconType;
export declare const IoWatch: IconType;
export declare const IoWaterOutline: IconType;
export declare const IoWaterSharp: IconType;
export declare const IoWater: IconType;
export declare const IoWifiOutline: IconType;
export declare const IoWifiSharp: IconType;
export declare const IoWifi: IconType;
export declare const IoWineOutline: IconType;
export declare const IoWineSharp: IconType;
export declare const IoWine: IconType;
export declare const IoWomanOutline: IconType;
export declare const IoWomanSharp: IconType;
export declare const IoWoman: IconType;
