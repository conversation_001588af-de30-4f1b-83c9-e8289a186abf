import { useState } from 'react'
import { motion } from 'framer-motion'
import styled from 'styled-components'
import { FaGamepad, FaPalette, FaCalculator, FaFont, FaPuzzlePiece, FaBrain } from 'react-icons/fa'
import MemoryGame from './components/MemoryGame'
import ColorsGame from './components/ColorsGame'
import NumbersGame from './components/NumbersGame'
import LettersGame from './components/LettersGame'
import DrawingGame from './components/DrawingGame'
import PuzzleGame from './components/PuzzleGame'
import './App.css'

// Styled Components
const AppContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Comic Sans MS', cursive, sans-serif;
`

const Header = styled.header`
  text-align: center;
  margin-bottom: 40px;
`

const Title = styled.h1`
  color: white;
  font-size: 3rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  margin-bottom: 10px;
`

const Subtitle = styled.p`
  color: #f0f0f0;
  font-size: 1.2rem;
  margin: 0;
`

const GamesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
`

const GameCard = styled(motion.div)`
  background: white;
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px);
  }
`

const GameIcon = styled.div`
  font-size: 4rem;
  margin-bottom: 20px;
  color: ${props => props.color};
`

const GameTitle = styled.h3`
  color: #333;
  font-size: 1.5rem;
  margin-bottom: 15px;
`

const GameDescription = styled.p`
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
`

function App() {
  const [selectedGame, setSelectedGame] = useState(null)

  const games = [
    {
      id: 'memory',
      title: 'لعبة الذاكرة',
      description: 'اختبر ذاكرتك مع البطاقات الملونة',
      icon: FaBrain,
      color: '#FF6B6B'
    },
    {
      id: 'colors',
      title: 'تعلم الألوان',
      description: 'اكتشف الألوان والأشكال الجميلة',
      icon: FaPalette,
      color: '#4ECDC4'
    },
    {
      id: 'numbers',
      title: 'الأرقام المرحة',
      description: 'تعلم العد والأرقام بطريقة ممتعة',
      icon: FaCalculator,
      color: '#45B7D1'
    },
    {
      id: 'letters',
      title: 'عالم الحروف',
      description: 'تعلم الحروف العربية والإنجليزية',
      icon: FaFont,
      color: '#96CEB4'
    },
    {
      id: 'drawing',
      title: 'لوحة الرسم',
      description: 'ارسم وأبدع بالألوان الزاهية',
      icon: FaPalette,
      color: '#FFEAA7'
    },
    {
      id: 'puzzle',
      title: 'الألغاز الذكية',
      description: 'حل الألغاز وطور مهاراتك',
      icon: FaPuzzlePiece,
      color: '#DDA0DD'
    }
  ]

  const handleGameSelect = (gameId) => {
    setSelectedGame(gameId)
  }

  const renderGame = () => {
    switch (selectedGame) {
      case 'memory':
        return <MemoryGame />
      case 'colors':
        return <ColorsGame />
      case 'numbers':
        return <NumbersGame />
      case 'letters':
        return <LettersGame />
      case 'drawing':
        return <DrawingGame />
      case 'puzzle':
        return <PuzzleGame />
      default:
        return <div style={{ textAlign: 'center', color: 'white', fontSize: '1.5rem' }}>لعبة غير موجودة!</div>
    }
  }

  if (selectedGame) {
    return (
      <AppContainer>
        <Header>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setSelectedGame(null)}
            style={{
              background: '#FF6B6B',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '10px',
              fontSize: '1rem',
              cursor: 'pointer',
              marginBottom: '20px'
            }}
          >
            العودة للقائمة الرئيسية
          </motion.button>
          <Title>لعبة {games.find(g => g.id === selectedGame)?.title}</Title>
        </Header>
        {renderGame()}
      </AppContainer>
    )
  }

  return (
    <AppContainer>
      <Header>
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Title>🎮 عالم الألعاب المرح 🎮</Title>
          <Subtitle>ألعاب تعليمية ممتعة للأطفال</Subtitle>
        </motion.div>
      </Header>

      <GamesGrid>
        {games.map((game, index) => (
          <GameCard
            key={game.id}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleGameSelect(game.id)}
          >
            <GameIcon color={game.color}>
              <game.icon />
            </GameIcon>
            <GameTitle>{game.title}</GameTitle>
            <GameDescription>{game.description}</GameDescription>
          </GameCard>
        ))}
      </GamesGrid>
    </AppContainer>
  )
}

export default App
