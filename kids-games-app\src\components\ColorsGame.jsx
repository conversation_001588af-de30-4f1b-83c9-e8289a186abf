import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import styled from 'styled-components'

const GameContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
`

const QuestionCard = styled.div`
  background: white;
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  margin-bottom: 30px;
`

const ColorDisplay = styled.div`
  width: 150px;
  height: 150px;
  background: ${props => props.color};
  border-radius: 50%;
  margin: 20px auto;
  box-shadow: 0 8px 16px rgba(0,0,0,0.2);
  border: 5px solid white;
`

const OptionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin: 30px 0;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`

const OptionButton = styled(motion.button)`
  background: ${props => props.isCorrect ? '#4CAF50' : props.isWrong ? '#F44336' : '#f0f0f0'};
  color: ${props => props.isCorrect || props.isWrong ? 'white' : '#333'};
  border: none;
  padding: 20px;
  border-radius: 15px;
  font-size: 1.3rem;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
`

const ScoreBoard = styled.div`
  background: white;
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  margin-bottom: 20px;
`

const NextButton = styled(motion.button)`
  background: #2196F3;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 10px;
  font-size: 1.1rem;
  cursor: pointer;
  margin: 20px 10px;
`

const ColorsGame = () => {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [score, setScore] = useState(0)
  const [selectedAnswer, setSelectedAnswer] = useState(null)
  const [showResult, setShowResult] = useState(false)
  const [gameCompleted, setGameCompleted] = useState(false)

  const colors = [
    { name: 'أحمر', nameEn: 'Red', color: '#FF5722' },
    { name: 'أزرق', nameEn: 'Blue', color: '#2196F3' },
    { name: 'أخضر', nameEn: 'Green', color: '#4CAF50' },
    { name: 'أصفر', nameEn: 'Yellow', color: '#FFEB3B' },
    { name: 'بنفسجي', nameEn: 'Purple', color: '#9C27B0' },
    { name: 'برتقالي', nameEn: 'Orange', color: '#FF9800' },
    { name: 'وردي', nameEn: 'Pink', color: '#E91E63' },
    { name: 'بني', nameEn: 'Brown', color: '#795548' },
    { name: 'رمادي', nameEn: 'Gray', color: '#9E9E9E' },
    { name: 'أسود', nameEn: 'Black', color: '#212121' }
  ]

  const [questions, setQuestions] = useState([])

  const generateQuestions = () => {
    const shuffledColors = [...colors].sort(() => Math.random() - 0.5)
    const gameQuestions = shuffledColors.slice(0, 5).map(correctColor => {
      const wrongOptions = colors
        .filter(c => c.name !== correctColor.name)
        .sort(() => Math.random() - 0.5)
        .slice(0, 3)
      
      const options = [correctColor, ...wrongOptions].sort(() => Math.random() - 0.5)
      
      return {
        color: correctColor.color,
        correctAnswer: correctColor.name,
        options: options.map(opt => opt.name)
      }
    })
    
    setQuestions(gameQuestions)
  }

  useEffect(() => {
    generateQuestions()
  }, [])

  const handleAnswerSelect = (answer) => {
    if (selectedAnswer !== null) return
    
    setSelectedAnswer(answer)
    setShowResult(true)
    
    if (answer === questions[currentQuestion].correctAnswer) {
      setScore(score + 20)
    }
  }

  const handleNextQuestion = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
      setSelectedAnswer(null)
      setShowResult(false)
    } else {
      setGameCompleted(true)
    }
  }

  const resetGame = () => {
    setCurrentQuestion(0)
    setScore(0)
    setSelectedAnswer(null)
    setShowResult(false)
    setGameCompleted(false)
    generateQuestions()
  }

  if (questions.length === 0) {
    return <div>جاري التحميل...</div>
  }

  if (gameCompleted) {
    return (
      <GameContainer>
        <QuestionCard>
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <h2 style={{ color: '#333', marginBottom: '20px' }}>🎉 انتهت اللعبة! 🎉</h2>
            <div style={{ fontSize: '2rem', margin: '20px 0' }}>
              النتيجة النهائية: {score} / {questions.length * 20}
            </div>
            <div style={{ fontSize: '1.3rem', color: '#666', marginBottom: '30px' }}>
              {score >= questions.length * 16 ? 'ممتاز! 🌟' : 
               score >= questions.length * 12 ? 'جيد جداً! 👏' : 
               score >= questions.length * 8 ? 'جيد! 👍' : 'حاول مرة أخرى! 💪'}
            </div>
            <NextButton
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={resetGame}
            >
              لعب مرة أخرى
            </NextButton>
          </motion.div>
        </QuestionCard>
      </GameContainer>
    )
  }

  const currentQ = questions[currentQuestion]

  return (
    <GameContainer>
      <ScoreBoard>
        <h2 style={{ color: '#333', marginBottom: '15px' }}>🎨 تعلم الألوان</h2>
        <div style={{ fontSize: '1.2rem', color: '#666' }}>
          السؤال {currentQuestion + 1} من {questions.length}
        </div>
        <div style={{ fontSize: '1.3rem', color: '#333', margin: '10px 0' }}>
          النقاط: {score}
        </div>
      </ScoreBoard>

      <QuestionCard>
        <h3 style={{ color: '#333', marginBottom: '20px' }}>ما هو اسم هذا اللون؟</h3>
        <ColorDisplay color={currentQ.color} />
        
        <OptionsGrid>
          {currentQ.options.map((option, index) => (
            <OptionButton
              key={index}
              onClick={() => handleAnswerSelect(option)}
              disabled={selectedAnswer !== null}
              isCorrect={showResult && option === currentQ.correctAnswer}
              isWrong={showResult && selectedAnswer === option && option !== currentQ.correctAnswer}
              whileHover={{ scale: selectedAnswer === null ? 1.05 : 1 }}
              whileTap={{ scale: selectedAnswer === null ? 0.95 : 1 }}
            >
              {option}
            </OptionButton>
          ))}
        </OptionsGrid>

        {showResult && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            style={{ marginTop: '20px' }}
          >
            <div style={{ 
              fontSize: '1.3rem', 
              color: selectedAnswer === currentQ.correctAnswer ? '#4CAF50' : '#F44336',
              marginBottom: '15px'
            }}>
              {selectedAnswer === currentQ.correctAnswer ? '✅ إجابة صحيحة!' : '❌ إجابة خاطئة!'}
            </div>
            {selectedAnswer !== currentQ.correctAnswer && (
              <div style={{ fontSize: '1.1rem', color: '#666', marginBottom: '15px' }}>
                الإجابة الصحيحة: {currentQ.correctAnswer}
              </div>
            )}
            <NextButton
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleNextQuestion}
            >
              {currentQuestion < questions.length - 1 ? 'السؤال التالي' : 'إنهاء اللعبة'}
            </NextButton>
          </motion.div>
        )}
      </QuestionCard>
    </GameContainer>
  )
}

export default ColorsGame
