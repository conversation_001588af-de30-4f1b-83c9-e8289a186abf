import { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import styled from 'styled-components'
import { Fa<PERSON><PERSON><PERSON>, FaEraser, FaTrash, FaDownload, FaUndo } from 'react-icons/fa'

const GameContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
`

const ToolBar = styled.div`
  background: white;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  justify-content: center;
`

const ColorPalette = styled.div`
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
`

const ColorButton = styled(motion.button)`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: ${props => props.active ? '3px solid #333' : '2px solid #ddd'};
  background: ${props => props.color};
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
`

const ToolButton = styled(motion.button)`
  background: ${props => props.active ? '#2196F3' : '#f0f0f0'};
  color: ${props => props.active ? 'white' : '#333'};
  border: none;
  padding: 12px 16px;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
`

const BrushSizeSlider = styled.input`
  width: 100px;
  margin: 0 10px;
`

const CanvasContainer = styled.div`
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  text-align: center;
`

const Canvas = styled.canvas`
  border: 2px solid #ddd;
  border-radius: 10px;
  cursor: ${props => props.tool === 'eraser' ? 'crosshair' : 'crosshair'};
  touch-action: none;
`

const DrawingGame = () => {
  const canvasRef = useRef(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [currentColor, setCurrentColor] = useState('#FF6B6B')
  const [brushSize, setBrushSize] = useState(5)
  const [tool, setTool] = useState('brush') // 'brush', 'eraser'
  const [history, setHistory] = useState([])
  const [historyIndex, setHistoryIndex] = useState(-1)

  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
    '#98D8C8', '#F7DC6F', '#FF9800', '#E91E63', '#9C27B0', '#795548',
    '#607D8B', '#000000', '#FFFFFF', '#FF5722'
  ]

  useEffect(() => {
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    
    // تعيين حجم الكانفاس
    canvas.width = 800
    canvas.height = 600
    
    // خلفية بيضاء
    ctx.fillStyle = 'white'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    
    // حفظ الحالة الأولية
    saveToHistory()
  }, [])

  const saveToHistory = () => {
    const canvas = canvasRef.current
    const imageData = canvas.toDataURL()
    const newHistory = history.slice(0, historyIndex + 1)
    newHistory.push(imageData)
    setHistory(newHistory)
    setHistoryIndex(newHistory.length - 1)
  }

  const startDrawing = (e) => {
    setIsDrawing(true)
    const canvas = canvasRef.current
    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    
    const ctx = canvas.getContext('2d')
    ctx.beginPath()
    ctx.moveTo(x, y)
  }

  const draw = (e) => {
    if (!isDrawing) return
    
    const canvas = canvasRef.current
    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    
    const ctx = canvas.getContext('2d')
    
    if (tool === 'brush') {
      ctx.globalCompositeOperation = 'source-over'
      ctx.strokeStyle = currentColor
    } else if (tool === 'eraser') {
      ctx.globalCompositeOperation = 'destination-out'
    }
    
    ctx.lineWidth = brushSize
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'
    
    ctx.lineTo(x, y)
    ctx.stroke()
  }

  const stopDrawing = () => {
    if (isDrawing) {
      setIsDrawing(false)
      saveToHistory()
    }
  }

  // دعم اللمس للأجهزة المحمولة
  const handleTouchStart = (e) => {
    e.preventDefault()
    const touch = e.touches[0]
    const mouseEvent = new MouseEvent('mousedown', {
      clientX: touch.clientX,
      clientY: touch.clientY
    })
    canvasRef.current.dispatchEvent(mouseEvent)
  }

  const handleTouchMove = (e) => {
    e.preventDefault()
    const touch = e.touches[0]
    const mouseEvent = new MouseEvent('mousemove', {
      clientX: touch.clientX,
      clientY: touch.clientY
    })
    canvasRef.current.dispatchEvent(mouseEvent)
  }

  const handleTouchEnd = (e) => {
    e.preventDefault()
    const mouseEvent = new MouseEvent('mouseup', {})
    canvasRef.current.dispatchEvent(mouseEvent)
  }

  const clearCanvas = () => {
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    ctx.fillStyle = 'white'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    saveToHistory()
  }

  const undo = () => {
    if (historyIndex > 0) {
      const canvas = canvasRef.current
      const ctx = canvas.getContext('2d')
      const img = new Image()
      
      img.onload = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        ctx.drawImage(img, 0, 0)
      }
      
      setHistoryIndex(historyIndex - 1)
      img.src = history[historyIndex - 1]
    }
  }

  const downloadDrawing = () => {
    const canvas = canvasRef.current
    const link = document.createElement('a')
    link.download = 'my-drawing.png'
    link.href = canvas.toDataURL()
    link.click()
  }

  return (
    <GameContainer>
      <ToolBar>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <span style={{ fontWeight: 'bold', color: '#333' }}>الألوان:</span>
          <ColorPalette>
            {colors.map((color, index) => (
              <ColorButton
                key={index}
                color={color}
                active={currentColor === color}
                onClick={() => setCurrentColor(color)}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              />
            ))}
          </ColorPalette>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <span style={{ fontWeight: 'bold', color: '#333' }}>الأدوات:</span>
          <ToolButton
            active={tool === 'brush'}
            onClick={() => setTool('brush')}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FaPalette /> فرشاة
          </ToolButton>
          <ToolButton
            active={tool === 'eraser'}
            onClick={() => setTool('eraser')}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FaEraser /> ممحاة
          </ToolButton>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <span style={{ fontWeight: 'bold', color: '#333' }}>حجم الفرشاة:</span>
          <BrushSizeSlider
            type="range"
            min="1"
            max="50"
            value={brushSize}
            onChange={(e) => setBrushSize(e.target.value)}
          />
          <span style={{ color: '#666' }}>{brushSize}px</span>
        </div>

        <div style={{ display: 'flex', gap: '10px' }}>
          <ToolButton
            onClick={undo}
            disabled={historyIndex <= 0}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FaUndo /> تراجع
          </ToolButton>
          <ToolButton
            onClick={clearCanvas}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{ background: '#FF6B6B', color: 'white' }}
          >
            <FaTrash /> مسح الكل
          </ToolButton>
          <ToolButton
            onClick={downloadDrawing}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{ background: '#4CAF50', color: 'white' }}
          >
            <FaDownload /> تحميل
          </ToolButton>
        </div>
      </ToolBar>

      <CanvasContainer>
        <h3 style={{ color: '#333', marginBottom: '20px' }}>🎨 لوحة الرسم السحرية</h3>
        <p style={{ color: '#666', marginBottom: '20px' }}>
          ارسم ما تشاء بالألوان الجميلة! استخدم الفأرة أو اللمس للرسم
        </p>
        <Canvas
          ref={canvasRef}
          tool={tool}
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        />
      </CanvasContainer>
    </GameContainer>
  )
}

export default DrawingGame
