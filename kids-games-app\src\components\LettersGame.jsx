import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import styled from 'styled-components'

const GameContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
`

const QuestionCard = styled.div`
  background: white;
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  margin-bottom: 30px;
`

const LetterDisplay = styled.div`
  font-size: 8rem;
  color: #4CAF50;
  font-weight: bold;
  margin: 30px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  font-family: 'Amiri', serif;
`

const OptionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin: 30px 0;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`

const OptionButton = styled(motion.button)`
  background: ${props => props.isCorrect ? '#4CAF50' : props.isWrong ? '#F44336' : '#f0f0f0'};
  color: ${props => props.isCorrect || props.isWrong ? 'white' : '#333'};
  border: none;
  padding: 20px;
  border-radius: 15px;
  font-size: 1.3rem;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
`

const ScoreBoard = styled.div`
  background: white;
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  margin-bottom: 20px;
`

const NextButton = styled(motion.button)`
  background: #9C27B0;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 10px;
  font-size: 1.1rem;
  cursor: pointer;
  margin: 20px 10px;
`

const ModeSelector = styled.div`
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
`

const ModeButton = styled(motion.button)`
  background: ${props => props.active ? '#2196F3' : '#f0f0f0'};
  color: ${props => props.active ? 'white' : '#333'};
  border: none;
  padding: 10px 20px;
  border-radius: 10px;
  font-size: 1rem;
  cursor: pointer;
`

const LettersGame = () => {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [score, setScore] = useState(0)
  const [selectedAnswer, setSelectedAnswer] = useState(null)
  const [showResult, setShowResult] = useState(false)
  const [gameCompleted, setGameCompleted] = useState(false)
  const [gameMode, setGameMode] = useState('arabic') // 'arabic', 'english', 'mixed'

  const arabicLetters = [
    { letter: 'أ', name: 'ألف', sound: 'أ' },
    { letter: 'ب', name: 'باء', sound: 'ب' },
    { letter: 'ت', name: 'تاء', sound: 'ت' },
    { letter: 'ث', name: 'ثاء', sound: 'ث' },
    { letter: 'ج', name: 'جيم', sound: 'ج' },
    { letter: 'ح', name: 'حاء', sound: 'ح' },
    { letter: 'خ', name: 'خاء', sound: 'خ' },
    { letter: 'د', name: 'دال', sound: 'د' },
    { letter: 'ذ', name: 'ذال', sound: 'ذ' },
    { letter: 'ر', name: 'راء', sound: 'ر' },
    { letter: 'ز', name: 'زاي', sound: 'ز' },
    { letter: 'س', name: 'سين', sound: 'س' },
    { letter: 'ش', name: 'شين', sound: 'ش' },
    { letter: 'ص', name: 'صاد', sound: 'ص' },
    { letter: 'ض', name: 'ضاد', sound: 'ض' },
    { letter: 'ط', name: 'طاء', sound: 'ط' },
    { letter: 'ظ', name: 'ظاء', sound: 'ظ' },
    { letter: 'ع', name: 'عين', sound: 'ع' },
    { letter: 'غ', name: 'غين', sound: 'غ' },
    { letter: 'ف', name: 'فاء', sound: 'ف' },
    { letter: 'ق', name: 'قاف', sound: 'ق' },
    { letter: 'ك', name: 'كاف', sound: 'ك' },
    { letter: 'ل', name: 'لام', sound: 'ل' },
    { letter: 'م', name: 'ميم', sound: 'م' },
    { letter: 'ن', name: 'نون', sound: 'ن' },
    { letter: 'ه', name: 'هاء', sound: 'ه' },
    { letter: 'و', name: 'واو', sound: 'و' },
    { letter: 'ي', name: 'ياء', sound: 'ي' }
  ]

  const englishLetters = [
    { letter: 'A', name: 'A', sound: 'إيه' },
    { letter: 'B', name: 'B', sound: 'بي' },
    { letter: 'C', name: 'C', sound: 'سي' },
    { letter: 'D', name: 'D', sound: 'دي' },
    { letter: 'E', name: 'E', sound: 'إي' },
    { letter: 'F', name: 'F', sound: 'إف' },
    { letter: 'G', name: 'G', sound: 'جي' },
    { letter: 'H', name: 'H', sound: 'إتش' },
    { letter: 'I', name: 'I', sound: 'آي' },
    { letter: 'J', name: 'J', sound: 'جيه' },
    { letter: 'K', name: 'K', sound: 'كيه' },
    { letter: 'L', name: 'L', sound: 'إل' },
    { letter: 'M', name: 'M', sound: 'إم' },
    { letter: 'N', name: 'N', sound: 'إن' },
    { letter: 'O', name: 'O', sound: 'أو' },
    { letter: 'P', name: 'P', sound: 'بي' },
    { letter: 'Q', name: 'Q', sound: 'كيو' },
    { letter: 'R', name: 'R', sound: 'آر' },
    { letter: 'S', name: 'S', sound: 'إس' },
    { letter: 'T', name: 'T', sound: 'تي' },
    { letter: 'U', name: 'U', sound: 'يو' },
    { letter: 'V', name: 'V', sound: 'في' },
    { letter: 'W', name: 'W', sound: 'دبليو' },
    { letter: 'X', name: 'X', sound: 'إكس' },
    { letter: 'Y', name: 'Y', sound: 'واي' },
    { letter: 'Z', name: 'Z', sound: 'زد' }
  ]

  const [questions, setQuestions] = useState([])

  const generateQuestions = () => {
    let letters = []
    switch (gameMode) {
      case 'arabic':
        letters = arabicLetters
        break
      case 'english':
        letters = englishLetters
        break
      case 'mixed':
        letters = [...arabicLetters.slice(0, 14), ...englishLetters.slice(0, 13)]
        break
      default:
        letters = arabicLetters
    }

    const shuffledLetters = [...letters].sort(() => Math.random() - 0.5)
    const gameQuestions = shuffledLetters.slice(0, 8).map(correctLetter => {
      const wrongOptions = letters
        .filter(l => l.name !== correctLetter.name)
        .sort(() => Math.random() - 0.5)
        .slice(0, 3)
      
      const options = [correctLetter.name, ...wrongOptions.map(opt => opt.name)]
        .sort(() => Math.random() - 0.5)
      
      return {
        letter: correctLetter.letter,
        correctAnswer: correctLetter.name,
        options: options
      }
    })
    
    setQuestions(gameQuestions)
  }

  useEffect(() => {
    generateQuestions()
  }, [gameMode])

  const handleAnswerSelect = (answer) => {
    if (selectedAnswer !== null) return
    
    setSelectedAnswer(answer)
    setShowResult(true)
    
    if (answer === questions[currentQuestion].correctAnswer) {
      setScore(score + 15)
    }
  }

  const handleNextQuestion = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
      setSelectedAnswer(null)
      setShowResult(false)
    } else {
      setGameCompleted(true)
    }
  }

  const resetGame = () => {
    setCurrentQuestion(0)
    setScore(0)
    setSelectedAnswer(null)
    setShowResult(false)
    setGameCompleted(false)
    generateQuestions()
  }

  const changeGameMode = (mode) => {
    setGameMode(mode)
    resetGame()
  }

  if (questions.length === 0) {
    return <div>جاري التحميل...</div>
  }

  if (gameCompleted) {
    return (
      <GameContainer>
        <QuestionCard>
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <h2 style={{ color: '#333', marginBottom: '20px' }}>🎉 انتهت اللعبة! 🎉</h2>
            <div style={{ fontSize: '2rem', margin: '20px 0' }}>
              النتيجة النهائية: {score} / {questions.length * 15}
            </div>
            <div style={{ fontSize: '1.3rem', color: '#666', marginBottom: '30px' }}>
              {score >= questions.length * 12 ? 'ممتاز! 🌟' : 
               score >= questions.length * 9 ? 'جيد جداً! 👏' : 
               score >= questions.length * 6 ? 'جيد! 👍' : 'حاول مرة أخرى! 💪'}
            </div>
            <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', flexWrap: 'wrap' }}>
              <NextButton
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={resetGame}
              >
                لعب مرة أخرى
              </NextButton>
            </div>
          </motion.div>
        </QuestionCard>
      </GameContainer>
    )
  }

  const currentQ = questions[currentQuestion]

  return (
    <GameContainer>
      <ScoreBoard>
        <h2 style={{ color: '#333', marginBottom: '15px' }}>📝 عالم الحروف</h2>
        
        <ModeSelector>
          <ModeButton
            active={gameMode === 'arabic'}
            onClick={() => changeGameMode('arabic')}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            العربية
          </ModeButton>
          <ModeButton
            active={gameMode === 'english'}
            onClick={() => changeGameMode('english')}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            English
          </ModeButton>
          <ModeButton
            active={gameMode === 'mixed'}
            onClick={() => changeGameMode('mixed')}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            مختلط
          </ModeButton>
        </ModeSelector>

        <div style={{ fontSize: '1.2rem', color: '#666' }}>
          السؤال {currentQuestion + 1} من {questions.length}
        </div>
        <div style={{ fontSize: '1.3rem', color: '#333', margin: '10px 0' }}>
          النقاط: {score}
        </div>
      </ScoreBoard>

      <QuestionCard>
        <h3 style={{ color: '#333', marginBottom: '20px' }}>ما هو اسم هذا الحرف؟</h3>
        <LetterDisplay>{currentQ.letter}</LetterDisplay>
        
        <OptionsGrid>
          {currentQ.options.map((option, index) => (
            <OptionButton
              key={index}
              onClick={() => handleAnswerSelect(option)}
              disabled={selectedAnswer !== null}
              isCorrect={showResult && option === currentQ.correctAnswer}
              isWrong={showResult && selectedAnswer === option && option !== currentQ.correctAnswer}
              whileHover={{ scale: selectedAnswer === null ? 1.05 : 1 }}
              whileTap={{ scale: selectedAnswer === null ? 0.95 : 1 }}
            >
              {option}
            </OptionButton>
          ))}
        </OptionsGrid>

        {showResult && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            style={{ marginTop: '20px' }}
          >
            <div style={{ 
              fontSize: '1.3rem', 
              color: selectedAnswer === currentQ.correctAnswer ? '#4CAF50' : '#F44336',
              marginBottom: '15px'
            }}>
              {selectedAnswer === currentQ.correctAnswer ? '✅ إجابة صحيحة!' : '❌ إجابة خاطئة!'}
            </div>
            {selectedAnswer !== currentQ.correctAnswer && (
              <div style={{ fontSize: '1.1rem', color: '#666', marginBottom: '15px' }}>
                الإجابة الصحيحة: {currentQ.correctAnswer}
              </div>
            )}
            <NextButton
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleNextQuestion}
            >
              {currentQuestion < questions.length - 1 ? 'السؤال التالي' : 'إنهاء اللعبة'}
            </NextButton>
          </motion.div>
        )}
      </QuestionCard>
    </GameContainer>
  )
}

export default LettersGame
