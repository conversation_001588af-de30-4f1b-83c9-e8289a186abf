import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import styled from 'styled-components'

const GameContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
`

const GameBoard = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin: 30px 0;
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }
`

const Card = styled(motion.div)`
  aspect-ratio: 1;
  background: ${props => props.isFlipped ? props.color : '#f0f0f0'};
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  border: 3px solid ${props => props.isMatched ? '#4CAF50' : 'transparent'};
  
  &:hover {
    transform: scale(1.05);
  }
`

const ScoreBoard = styled.div`
  background: white;
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  margin-bottom: 20px;
`

const Score = styled.div`
  font-size: 1.2rem;
  color: #333;
  margin: 10px 0;
`

const ResetButton = styled(motion.button)`
  background: #FF6B6B;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 10px;
  font-size: 1.1rem;
  cursor: pointer;
  margin: 10px;
`

const MemoryGame = () => {
  const [cards, setCards] = useState([])
  const [flippedCards, setFlippedCards] = useState([])
  const [matchedCards, setMatchedCards] = useState([])
  const [moves, setMoves] = useState(0)
  const [score, setScore] = useState(0)
  const [gameWon, setGameWon] = useState(false)

  const cardSymbols = ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼']
  const cardColors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']

  const initializeGame = () => {
    const gameCards = []
    cardSymbols.forEach((symbol, index) => {
      gameCards.push(
        { id: index * 2, symbol, color: cardColors[index], isFlipped: false, isMatched: false },
        { id: index * 2 + 1, symbol, color: cardColors[index], isFlipped: false, isMatched: false }
      )
    })
    
    // خلط البطاقات
    const shuffledCards = gameCards.sort(() => Math.random() - 0.5)
    setCards(shuffledCards)
    setFlippedCards([])
    setMatchedCards([])
    setMoves(0)
    setScore(0)
    setGameWon(false)
  }

  useEffect(() => {
    initializeGame()
  }, [])

  useEffect(() => {
    if (flippedCards.length === 2) {
      const [first, second] = flippedCards
      setMoves(moves + 1)
      
      if (cards[first].symbol === cards[second].symbol) {
        // تطابق!
        setMatchedCards([...matchedCards, first, second])
        setScore(score + 10)
        setFlippedCards([])
        
        // تحديث حالة البطاقات
        setCards(prevCards => 
          prevCards.map((card, index) => 
            index === first || index === second 
              ? { ...card, isMatched: true }
              : card
          )
        )
      } else {
        // عدم تطابق
        setTimeout(() => {
          setFlippedCards([])
          setCards(prevCards => 
            prevCards.map((card, index) => 
              index === first || index === second 
                ? { ...card, isFlipped: false }
                : card
            )
          )
        }, 1000)
      }
    }
  }, [flippedCards])

  useEffect(() => {
    if (matchedCards.length === cards.length && cards.length > 0) {
      setGameWon(true)
      setScore(score + Math.max(0, 100 - moves * 2)) // مكافأة إضافية للحركات القليلة
    }
  }, [matchedCards])

  const handleCardClick = (index) => {
    if (flippedCards.length === 2 || flippedCards.includes(index) || cards[index].isMatched) {
      return
    }

    setCards(prevCards => 
      prevCards.map((card, i) => 
        i === index ? { ...card, isFlipped: true } : card
      )
    )
    setFlippedCards([...flippedCards, index])
  }

  return (
    <GameContainer>
      <ScoreBoard>
        <h2 style={{ color: '#333', marginBottom: '15px' }}>🧠 لعبة الذاكرة</h2>
        <Score>النقاط: {score}</Score>
        <Score>الحركات: {moves}</Score>
        {gameWon && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            style={{ color: '#4CAF50', fontSize: '1.5rem', margin: '15px 0' }}
          >
            🎉 أحسنت! لقد فزت! 🎉
          </motion.div>
        )}
        <ResetButton
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={initializeGame}
        >
          لعبة جديدة
        </ResetButton>
      </ScoreBoard>

      <GameBoard>
        {cards.map((card, index) => (
          <Card
            key={card.id}
            isFlipped={card.isFlipped || card.isMatched}
            isMatched={card.isMatched}
            color={card.color}
            onClick={() => handleCardClick(index)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            initial={{ rotateY: 0 }}
            animate={{ rotateY: card.isFlipped || card.isMatched ? 0 : 0 }}
            transition={{ duration: 0.3 }}
          >
            {(card.isFlipped || card.isMatched) ? card.symbol : '?'}
          </Card>
        ))}
      </GameBoard>
    </GameContainer>
  )
}

export default MemoryGame
