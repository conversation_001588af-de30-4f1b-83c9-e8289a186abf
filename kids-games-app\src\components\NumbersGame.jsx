import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import styled from 'styled-components'

const GameContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
`

const QuestionCard = styled.div`
  background: white;
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  margin-bottom: 30px;
`

const NumberDisplay = styled.div`
  font-size: 6rem;
  color: #2196F3;
  font-weight: bold;
  margin: 30px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
`

const ObjectsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  margin: 30px 0;
  min-height: 100px;
  align-items: center;
`

const ObjectItem = styled(motion.div)`
  font-size: 2.5rem;
  margin: 5px;
`

const OptionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin: 30px 0;
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
`

const OptionButton = styled(motion.button)`
  background: ${props => props.isCorrect ? '#4CAF50' : props.isWrong ? '#F44336' : '#f0f0f0'};
  color: ${props => props.isCorrect || props.isWrong ? 'white' : '#333'};
  border: none;
  padding: 20px;
  border-radius: 15px;
  font-size: 2rem;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
`

const ScoreBoard = styled.div`
  background: white;
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  margin-bottom: 20px;
`

const NextButton = styled(motion.button)`
  background: #FF9800;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 10px;
  font-size: 1.1rem;
  cursor: pointer;
  margin: 20px 10px;
`

const NumbersGame = () => {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [score, setScore] = useState(0)
  const [selectedAnswer, setSelectedAnswer] = useState(null)
  const [showResult, setShowResult] = useState(false)
  const [gameCompleted, setGameCompleted] = useState(false)
  const [gameMode, setGameMode] = useState('count') // 'count', 'recognize', 'math'

  const objects = ['🍎', '🍌', '🍊', '🎈', '⭐', '🌸', '🦋', '🐝', '🍓', '🍇']

  const [questions, setQuestions] = useState([])

  const generateCountingQuestions = () => {
    const questions = []
    for (let i = 0; i < 5; i++) {
      const correctAnswer = Math.floor(Math.random() * 10) + 1
      const objectType = objects[Math.floor(Math.random() * objects.length)]
      const wrongAnswers = []
      
      while (wrongAnswers.length < 5) {
        const wrong = Math.floor(Math.random() * 10) + 1
        if (wrong !== correctAnswer && !wrongAnswers.includes(wrong)) {
          wrongAnswers.push(wrong)
        }
      }
      
      const options = [correctAnswer, ...wrongAnswers.slice(0, 5)]
        .sort(() => Math.random() - 0.5)
      
      questions.push({
        type: 'count',
        question: 'كم عدد الأشياء؟',
        objects: Array(correctAnswer).fill(objectType),
        correctAnswer,
        options
      })
    }
    return questions
  }

  const generateRecognitionQuestions = () => {
    const questions = []
    for (let i = 0; i < 5; i++) {
      const correctAnswer = Math.floor(Math.random() * 20) + 1
      const wrongAnswers = []
      
      while (wrongAnswers.length < 5) {
        const wrong = Math.floor(Math.random() * 20) + 1
        if (wrong !== correctAnswer && !wrongAnswers.includes(wrong)) {
          wrongAnswers.push(wrong)
        }
      }
      
      const options = [correctAnswer, ...wrongAnswers.slice(0, 5)]
        .sort(() => Math.random() - 0.5)
      
      questions.push({
        type: 'recognize',
        question: 'ما هو هذا الرقم؟',
        number: correctAnswer,
        correctAnswer,
        options
      })
    }
    return questions
  }

  const generateMathQuestions = () => {
    const questions = []
    for (let i = 0; i < 5; i++) {
      const num1 = Math.floor(Math.random() * 5) + 1
      const num2 = Math.floor(Math.random() * 5) + 1
      const operation = Math.random() > 0.5 ? '+' : '-'
      
      let correctAnswer, question
      if (operation === '+') {
        correctAnswer = num1 + num2
        question = `${num1} + ${num2} = ؟`
      } else {
        if (num1 >= num2) {
          correctAnswer = num1 - num2
          question = `${num1} - ${num2} = ؟`
        } else {
          correctAnswer = num2 - num1
          question = `${num2} - ${num1} = ؟`
        }
      }
      
      const wrongAnswers = []
      while (wrongAnswers.length < 5) {
        const wrong = Math.floor(Math.random() * 10)
        if (wrong !== correctAnswer && !wrongAnswers.includes(wrong) && wrong >= 0) {
          wrongAnswers.push(wrong)
        }
      }
      
      const options = [correctAnswer, ...wrongAnswers.slice(0, 5)]
        .sort(() => Math.random() - 0.5)
      
      questions.push({
        type: 'math',
        question,
        correctAnswer,
        options
      })
    }
    return questions
  }

  const generateQuestions = () => {
    let newQuestions = []
    switch (gameMode) {
      case 'count':
        newQuestions = generateCountingQuestions()
        break
      case 'recognize':
        newQuestions = generateRecognitionQuestions()
        break
      case 'math':
        newQuestions = generateMathQuestions()
        break
      default:
        newQuestions = generateCountingQuestions()
    }
    setQuestions(newQuestions)
  }

  useEffect(() => {
    generateQuestions()
  }, [gameMode])

  const handleAnswerSelect = (answer) => {
    if (selectedAnswer !== null) return
    
    setSelectedAnswer(answer)
    setShowResult(true)
    
    if (answer === questions[currentQuestion].correctAnswer) {
      setScore(score + 20)
    }
  }

  const handleNextQuestion = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
      setSelectedAnswer(null)
      setShowResult(false)
    } else {
      setGameCompleted(true)
    }
  }

  const resetGame = () => {
    setCurrentQuestion(0)
    setScore(0)
    setSelectedAnswer(null)
    setShowResult(false)
    setGameCompleted(false)
    generateQuestions()
  }

  const changeGameMode = (mode) => {
    setGameMode(mode)
    resetGame()
  }

  if (questions.length === 0) {
    return <div>جاري التحميل...</div>
  }

  if (gameCompleted) {
    return (
      <GameContainer>
        <QuestionCard>
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <h2 style={{ color: '#333', marginBottom: '20px' }}>🎉 انتهت اللعبة! 🎉</h2>
            <div style={{ fontSize: '2rem', margin: '20px 0' }}>
              النتيجة النهائية: {score} / {questions.length * 20}
            </div>
            <div style={{ fontSize: '1.3rem', color: '#666', marginBottom: '30px' }}>
              {score >= questions.length * 16 ? 'ممتاز! 🌟' : 
               score >= questions.length * 12 ? 'جيد جداً! 👏' : 
               score >= questions.length * 8 ? 'جيد! 👍' : 'حاول مرة أخرى! 💪'}
            </div>
            <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', flexWrap: 'wrap' }}>
              <NextButton
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={resetGame}
              >
                لعب مرة أخرى
              </NextButton>
              <NextButton
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => changeGameMode('count')}
                style={{ background: '#4CAF50' }}
              >
                العد
              </NextButton>
              <NextButton
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => changeGameMode('recognize')}
                style={{ background: '#2196F3' }}
              >
                التعرف على الأرقام
              </NextButton>
              <NextButton
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => changeGameMode('math')}
                style={{ background: '#9C27B0' }}
              >
                الحساب
              </NextButton>
            </div>
          </motion.div>
        </QuestionCard>
      </GameContainer>
    )
  }

  const currentQ = questions[currentQuestion]

  return (
    <GameContainer>
      <ScoreBoard>
        <h2 style={{ color: '#333', marginBottom: '15px' }}>🔢 الأرقام المرحة</h2>
        <div style={{ fontSize: '1rem', color: '#666', marginBottom: '10px' }}>
          النمط: {gameMode === 'count' ? 'العد' : gameMode === 'recognize' ? 'التعرف على الأرقام' : 'الحساب'}
        </div>
        <div style={{ fontSize: '1.2rem', color: '#666' }}>
          السؤال {currentQuestion + 1} من {questions.length}
        </div>
        <div style={{ fontSize: '1.3rem', color: '#333', margin: '10px 0' }}>
          النقاط: {score}
        </div>
      </ScoreBoard>

      <QuestionCard>
        <h3 style={{ color: '#333', marginBottom: '20px' }}>{currentQ.question}</h3>
        
        {currentQ.type === 'count' && (
          <ObjectsContainer>
            {currentQ.objects.map((obj, index) => (
              <ObjectItem
                key={index}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                {obj}
              </ObjectItem>
            ))}
          </ObjectsContainer>
        )}
        
        {currentQ.type === 'recognize' && (
          <NumberDisplay>{currentQ.number}</NumberDisplay>
        )}
        
        {currentQ.type === 'math' && (
          <div style={{ fontSize: '3rem', color: '#333', margin: '30px 0', fontWeight: 'bold' }}>
            {currentQ.question}
          </div>
        )}
        
        <OptionsGrid>
          {currentQ.options.map((option, index) => (
            <OptionButton
              key={index}
              onClick={() => handleAnswerSelect(option)}
              disabled={selectedAnswer !== null}
              isCorrect={showResult && option === currentQ.correctAnswer}
              isWrong={showResult && selectedAnswer === option && option !== currentQ.correctAnswer}
              whileHover={{ scale: selectedAnswer === null ? 1.05 : 1 }}
              whileTap={{ scale: selectedAnswer === null ? 0.95 : 1 }}
            >
              {option}
            </OptionButton>
          ))}
        </OptionsGrid>

        {showResult && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            style={{ marginTop: '20px' }}
          >
            <div style={{ 
              fontSize: '1.3rem', 
              color: selectedAnswer === currentQ.correctAnswer ? '#4CAF50' : '#F44336',
              marginBottom: '15px'
            }}>
              {selectedAnswer === currentQ.correctAnswer ? '✅ إجابة صحيحة!' : '❌ إجابة خاطئة!'}
            </div>
            {selectedAnswer !== currentQ.correctAnswer && (
              <div style={{ fontSize: '1.1rem', color: '#666', marginBottom: '15px' }}>
                الإجابة الصحيحة: {currentQ.correctAnswer}
              </div>
            )}
            <NextButton
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleNextQuestion}
            >
              {currentQuestion < questions.length - 1 ? 'السؤال التالي' : 'إنهاء اللعبة'}
            </NextButton>
          </motion.div>
        )}
      </QuestionCard>
    </GameContainer>
  )
}

export default NumbersGame
