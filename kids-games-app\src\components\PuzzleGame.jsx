import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import styled from 'styled-components'

const GameContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
`

const PuzzleBoard = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  max-width: 400px;
  margin: 30px auto;
  background: white;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
`

const PuzzlePiece = styled(motion.div)`
  aspect-ratio: 1;
  background: ${props => props.isEmpty ? '#f0f0f0' : props.color};
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  color: white;
  cursor: ${props => props.isEmpty ? 'default' : 'pointer'};
  border: 2px solid ${props => props.isEmpty ? '#ddd' : 'transparent'};
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  
  &:hover {
    transform: ${props => props.isEmpty ? 'none' : 'scale(1.05)'};
  }
`

const ScoreBoard = styled.div`
  background: white;
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  margin-bottom: 20px;
`

const ControlButtons = styled.div`
  display: flex;
  gap: 15px;
  justify-content: center;
  margin: 20px 0;
  flex-wrap: wrap;
`

const Button = styled(motion.button)`
  background: ${props => props.color || '#2196F3'};
  color: white;
  border: none;
  padding: 15px 25px;
  border-radius: 10px;
  font-size: 1.1rem;
  cursor: pointer;
`

const DifficultySelector = styled.div`
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
`

const DifficultyButton = styled(motion.button)`
  background: ${props => props.active ? '#4CAF50' : '#f0f0f0'};
  color: ${props => props.active ? 'white' : '#333'};
  border: none;
  padding: 10px 20px;
  border-radius: 10px;
  font-size: 1rem;
  cursor: pointer;
`

const PuzzleGame = () => {
  const [board, setBoard] = useState([])
  const [moves, setMoves] = useState(0)
  const [isCompleted, setIsCompleted] = useState(false)
  const [difficulty, setDifficulty] = useState('easy') // 'easy', 'medium', 'hard'
  const [gameStarted, setGameStarted] = useState(false)

  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']

  const initializeBoard = () => {
    let size = 3
    if (difficulty === 'medium') size = 4
    if (difficulty === 'hard') size = 5

    const totalPieces = size * size
    const pieces = []
    
    for (let i = 1; i < totalPieces; i++) {
      pieces.push({
        number: i,
        color: colors[i % colors.length],
        isEmpty: false
      })
    }
    
    // إضافة المربع الفارغ
    pieces.push({
      number: null,
      color: null,
      isEmpty: true
    })

    // خلط القطع
    const shuffledPieces = shufflePuzzle([...pieces], size)
    setBoard(shuffledPieces)
    setMoves(0)
    setIsCompleted(false)
    setGameStarted(true)
  }

  const shufflePuzzle = (pieces, size) => {
    // خلط ذكي للتأكد من إمكانية الحل
    const shuffled = [...pieces]
    const totalMoves = difficulty === 'easy' ? 50 : difficulty === 'medium' ? 100 : 200
    
    for (let i = 0; i < totalMoves; i++) {
      const emptyIndex = shuffled.findIndex(piece => piece.isEmpty)
      const possibleMoves = getPossibleMoves(emptyIndex, size)
      const randomMove = possibleMoves[Math.floor(Math.random() * possibleMoves.length)]
      
      // تبديل القطعة الفارغة مع قطعة مجاورة
      [shuffled[emptyIndex], shuffled[randomMove]] = [shuffled[randomMove], shuffled[emptyIndex]]
    }
    
    return shuffled
  }

  const getPossibleMoves = (emptyIndex, size) => {
    const moves = []
    const row = Math.floor(emptyIndex / size)
    const col = emptyIndex % size
    
    // أعلى
    if (row > 0) moves.push(emptyIndex - size)
    // أسفل
    if (row < size - 1) moves.push(emptyIndex + size)
    // يسار
    if (col > 0) moves.push(emptyIndex - 1)
    // يمين
    if (col < size - 1) moves.push(emptyIndex + 1)
    
    return moves
  }

  const handlePieceClick = (clickedIndex) => {
    if (isCompleted) return
    
    const size = Math.sqrt(board.length)
    const emptyIndex = board.findIndex(piece => piece.isEmpty)
    const possibleMoves = getPossibleMoves(emptyIndex, size)
    
    if (possibleMoves.includes(clickedIndex)) {
      const newBoard = [...board]
      // تبديل القطعة المنقورة مع القطعة الفارغة
      [newBoard[emptyIndex], newBoard[clickedIndex]] = [newBoard[clickedIndex], newBoard[emptyIndex]]
      
      setBoard(newBoard)
      setMoves(moves + 1)
      
      // فحص الفوز
      checkWin(newBoard)
    }
  }

  const checkWin = (currentBoard) => {
    const size = Math.sqrt(currentBoard.length)
    const totalPieces = size * size
    
    for (let i = 0; i < totalPieces - 1; i++) {
      if (currentBoard[i].number !== i + 1) {
        return false
      }
    }
    
    // التأكد من أن القطعة الفارغة في المكان الصحيح
    if (!currentBoard[totalPieces - 1].isEmpty) {
      return false
    }
    
    setIsCompleted(true)
    return true
  }

  const changeDifficulty = (newDifficulty) => {
    setDifficulty(newDifficulty)
    setGameStarted(false)
    setBoard([])
  }

  const getBoardSize = () => {
    switch (difficulty) {
      case 'easy': return 3
      case 'medium': return 4
      case 'hard': return 5
      default: return 3
    }
  }

  const getDifficultyName = () => {
    switch (difficulty) {
      case 'easy': return 'سهل (3×3)'
      case 'medium': return 'متوسط (4×4)'
      case 'hard': return 'صعب (5×5)'
      default: return 'سهل'
    }
  }

  return (
    <GameContainer>
      <ScoreBoard>
        <h2 style={{ color: '#333', marginBottom: '15px' }}>🧩 الألغاز الذكية</h2>
        <p style={{ color: '#666', marginBottom: '20px' }}>
          رتب الأرقام من 1 إلى {getBoardSize() * getBoardSize() - 1} بالترتيب الصحيح
        </p>
        
        <DifficultySelector>
          <DifficultyButton
            active={difficulty === 'easy'}
            onClick={() => changeDifficulty('easy')}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            سهل (3×3)
          </DifficultyButton>
          <DifficultyButton
            active={difficulty === 'medium'}
            onClick={() => changeDifficulty('medium')}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            متوسط (4×4)
          </DifficultyButton>
          <DifficultyButton
            active={difficulty === 'hard'}
            onClick={() => changeDifficulty('hard')}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            صعب (5×5)
          </DifficultyButton>
        </DifficultySelector>

        {gameStarted && (
          <div style={{ fontSize: '1.2rem', color: '#333', margin: '10px 0' }}>
            عدد الحركات: {moves}
          </div>
        )}

        {isCompleted && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            style={{ color: '#4CAF50', fontSize: '1.5rem', margin: '15px 0' }}
          >
            🎉 أحسنت! لقد حللت اللغز في {moves} حركة! 🎉
          </motion.div>
        )}
      </ScoreBoard>

      {!gameStarted ? (
        <div style={{ textAlign: 'center' }}>
          <Button
            color="#4CAF50"
            onClick={initializeBoard}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            ابدأ اللعبة - {getDifficultyName()}
          </Button>
        </div>
      ) : (
        <>
          <PuzzleBoard
            style={{
              gridTemplateColumns: `repeat(${getBoardSize()}, 1fr)`,
              maxWidth: getBoardSize() * 80 + (getBoardSize() - 1) * 10 + 40
            }}
          >
            {board.map((piece, index) => (
              <PuzzlePiece
                key={index}
                isEmpty={piece.isEmpty}
                color={piece.color}
                onClick={() => handlePieceClick(index)}
                whileHover={{ scale: piece.isEmpty ? 1 : 1.05 }}
                whileTap={{ scale: piece.isEmpty ? 1 : 0.95 }}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: index * 0.05 }}
              >
                {piece.isEmpty ? '' : piece.number}
              </PuzzlePiece>
            ))}
          </PuzzleBoard>

          <ControlButtons>
            <Button
              color="#FF9800"
              onClick={initializeBoard}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              خلط جديد
            </Button>
            <Button
              color="#9C27B0"
              onClick={() => setGameStarted(false)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              تغيير المستوى
            </Button>
          </ControlButtons>
        </>
      )}
    </GameContainer>
  )
}

export default PuzzleGame
