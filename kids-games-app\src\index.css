/* تحسين الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Comic Sans MS', 'Cairo', cursive, sans-serif;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
}

.arabic-text {
  font-family: 'Cairo', 'Amiri', sans-serif;
  direction: rtl;
  text-align: right;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .games-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 10px;
  }

  .game-card {
    padding: 20px;
  }

  .title {
    font-size: 2rem !important;
  }
}

/* تأثيرات الحركة */
@keyframes bounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  80% {
    transform: translateY(-5px);
  }
}

.bounce {
  animation: bounce 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* تحسين إمكانية الوصول */
button:focus,
.game-card:focus {
  outline: 3px solid #FFD700;
  outline-offset: 2px;
}

/* تحسين التباين للنصوص */
.high-contrast {
  color: #000;
  background-color: #FFF;
  border: 2px solid #000;
}
